{"name": "mysnout", "version": "0.1.0", "private": true, "dependencies": {"@mantine/core": "^7.13.2", "@mantine/dropzone": "^7.13.2", "@mantine/form": "^7.13.2", "@mantine/hooks": "^7.13.2", "@tabler/icons-react": "^3.21.0", "@reduxjs/toolkit": "^2.3.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.14", "@types/node": "^22.10.1", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "axios": "^1.7.9", "bootstrap": "^5.3.3", "lodash.omit": "^4.5.0", "lodash.uniqueid": "^4.0.1", "react": "^18.3.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dnd-touch-backend": "^16.0.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.5", "react-image-gallery": "^1.3.0", "react-redux": "^9.1.2", "react-router-dom": "^6.28.0", "react-scripts": "^5.0.1", "redux-persist": "^6.0.0", "typescript": "^4.9.5", "web-vitals": "^4.2.4"}, "scripts": {"start": "react-scripts start", "build": "GENERATE_SOURCEMAP=false react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "analyze": "node ./analyze.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/lodash.omit": "^4.5.9", "@types/lodash.uniqueid": "^4.0.9"}}