{"name": "mysnout", "version": "0.1.0", "private": true, "dependencies": {"@mantine/core": "^4.2.7", "@mantine/dropzone": "^4.2.7", "@mantine/form": "^4.1.3", "@mantine/hooks": "^4.2.7", "@modulz/radix-icons": "4.0.0", "@reduxjs/toolkit": "^1.8.0", "@testing-library/jest-dom": "^5.15.0", "@testing-library/react": "^11.2.7", "@testing-library/user-event": "^12.8.3", "@types/jest": "^27.4.1", "@types/node": "^17.0.23", "@types/react": "^17.0.43", "@types/react-dom": "^17.0.14", "axios": "^0.24.0", "bootstrap": "^5.1.3", "lodash.omit": "^4.5.0", "lodash.uniqueid": "^4.0.1", "progress-bar-webpack-plugin": "^2.1.0", "react": "^17.0.2", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dnd-touch-backend": "^16.0.1", "react-dom": "^17.0.2", "react-dropzone": "^14.2.1", "react-image-gallery": "^1.2.8", "react-redux": "^7.2.6", "react-router-dom": "6.2.1", "react-scripts": "4.0.3", "redux-persist": "^6.0.0", "tabler-icons-react": "^1.48.0", "typescript": "^4.6.3", "web-vitals": "^1.1.2"}, "scripts": {"start": "react-scripts start", "build": "GENERATE_SOURCEMAP=false react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "analyze": "node ./analyze.js"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"react-router-dom": "^6.0.1"}}