// import { BrowserRouter as Router, Routes, Route, Link } from "react-router-dom";
// import Login from "./Admin/LOGIN/Login";
// import Signup from "./Admin/Signup/Signup";
// import Home from "./Admin/Home";
// import SignupWarp from "./Admin/Signup/SignupWarp";
// import OtpInput from "./Admin/Signup/OtpInput";
// import NavbarIn from "./Components/Navbar";
// import HostelDetails from "./Admin/HostelDetails/HostelDetailsWraper";
// import FirstStep from "./Admin/PasswordRecovery/FirstStep";
// import ForgotPassword from "./Admin/PasswordRecovery/ForgotPassword";
// import { SecondStep, ThirdStep } from "./Admin/PasswordRecovery/SecondStep";
// import MyHostels from "./Admin/HostelDetails/MyHostels";
// import {
//   AppShell,
//   Burger,
//   Header,
//   MediaQuery,
//   Navbar,
//   useMantineTheme,
// } from "@mantine/core";
// import DarkModeToggle from "./Components/DarkModeToggle";
// import React, { useState } from "react";
// export default function Admin() {
//   const [opened, setOpened] = useState(false);

//   const theme = useMantineTheme();

//   return (
//     <>
//       <Router>
//         <AppShell
//           navbarOffsetBreakpoint="sm"
//           fixed
//           padding="md"
//           navbar={
//             <Navbar
//               hiddenBreakpoint="sm"
//               width={{ sm: 200, lg: 300 }}
//               padding="md"
//               hidden={!opened}
//             >
//               <Navbar.Section>mysnout</Navbar.Section>
//               <Navbar.Section grow mt="lg">
//                 <Link to="/myhostels">My Hostels</Link>
//               </Navbar.Section>
//               <Navbar.Section>Admin</Navbar.Section>
//             </Navbar>
//           }
//           header={
//             <Header height={60} padding="md">
//               {/* Header content */}
//               <div
//                 style={{
//                   display: "flex",
//                   alignItems: "center",
//                   height: "100%",
//                 }}
//               >
//                 <MediaQuery largerThan="sm" styles={{ display: "none" }}>
//                   <Burger
//                     opened={opened}
//                     onClick={() => setOpened((o) => !o)}
//                     size="sm"
//                     color={theme.colors.gray[6]}
//                     mr="xl"
//                   />
//                 </MediaQuery>
//                 <NavbarIn />
//                 <DarkModeToggle />
//               </div>
//             </Header>
//           }
//           styles={(theme) => ({
//             main: {
//               backgroundColor:
//                 theme.colorScheme === "dark"
//                   ? theme.colors.dark[8]
//                   : theme.colors.gray[0],
//             },
//           })}
//         >
//           {/* Your application here */}
//           <Routes>
//             <Route path="/" element={<Home />} />
//             <Route path="/login" element={<Login />} />
//             <Route path="/signup" element={<SignupWarp />}>
//               <Route path="" element={<Signup />} />
//               <Route path="otp" element={<OtpInput />} />
//             </Route>
//             <Route path="/forgotpassword" element={<ForgotPassword />}>
//               <Route path="" element={<FirstStep />} />
//               <Route path="verifyOtp" element={<SecondStep />}></Route>
//               <Route path="reset" element={<ThirdStep />}></Route>
//             </Route>
//             <Route path="/hostledetails" element={<HostelDetails />} />
//             <Route path="/myhostels" element={<MyHostels />} />
//           </Routes>
//         </AppShell>
//       </Router>
//     </>
//   );
// }
