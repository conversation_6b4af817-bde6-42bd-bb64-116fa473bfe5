import "../Css/HostleDetails.css";
import { useSelector } from "react-redux";
import { Container } from "@mantine/core";

export default function Home() {
  const isLoggedIn = useSelector((store) => store.auth?.isLoggedIn);
  const userDetails = useSelector((store) => store.auth.user);
  const keys = isLoggedIn ? Object.keys(userDetails) : [];

  return (
    <>
      <Container style={{ wordBreak: "break-all" }}>
        <h1>Home</h1>
        {isLoggedIn &&
          keys.map((key) => (
            <h6 key={key}>{`${key} : ${userDetails[key]}`}</h6>
          ))}
      </Container>
    </>
  );
}
