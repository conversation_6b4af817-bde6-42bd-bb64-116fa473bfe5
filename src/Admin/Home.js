import "../Css/HostleDetails.css";
import { useSelector } from "react-redux";
import { Chip, Container, createStyles, Text } from "@mantine/core";
import ImageDropZone from "../Components/ImageDropzone";
const useStyles = createStyles((theme) => ({
  chip: {
    display: "inline-block",
    border: "1px solid green",
    padding: `${theme.radius.sm}px`,
    backgroundColor: "#25262B",
  },
}));

export default function Home() {
  // const dispatch = useDispatch();
  // dispatch({ type: "auth/login" });
  // dispatch({ type: "features/CallBegan" });
  // dispatch({ type: "features/onSuccess" });

  const { classes } = useStyles();
  const isLoggedIn = useSelector((store) => store.auth?.isLoggedIn);
  const userDetails = useSelector((store) => store.auth.user);
  const keys = isLoggedIn ? Object.keys(userDetails) : [];
  return (
    <>
      <Container style={{ wordBreak: "break-all" }}>
        <h1>Home</h1>
        {isLoggedIn &&
          keys.map((key) => (
            <h6 key={key}>{`${key} : ${userDetails[key]}`}</h6>
          ))}
        {/* <ImageDropZone></ImageDropZone> */}
      </Container>
    </>
  );
}
