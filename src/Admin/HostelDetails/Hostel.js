import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useParams } from "react-router-dom";
import { GetHostel } from "../../Store/hostels";
export default function Hostel() {
  const dispatch = useDispatch();
  const params = useParams();
  const hostel = useSelector((store) => store.hostels.hostelsList[params.hid]);

  useEffect(() => {
    if (!hostel) dispatch(GetHostel(params.hid));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  console.log(hostel);
  return (
    <>
      <h1>Hostel</h1>
      {hostel && JSON.stringify(hostel, undefined, 2)}
    </>
  );
}
// {hostel &&
//   Object.keys(hostel).map((key) => (
//     <div key={key}>{hostel[key].toString()}</div>
//   ))}
