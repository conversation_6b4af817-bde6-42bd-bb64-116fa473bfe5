import { Container } from "@mantine/core";
import React from "react";
import { Link } from "react-router-dom";
export default function HostelCard(props) {
  const hosteldetails = props.hostel;
  // console.log("hostelCardRendered");
  //   console.log(props);
  return (
    <>
      <Container
        style={{
          border: "1px solid skyblue",
          height: "215px",
          display: "block",
        }}
      >
        <div style={{ float: "right" }}>
          {hosteldetails?.hostelImages?.map((url) => (
            <img
              key={url}
              src={url}
              style={{
                width: "200px",
                height: "200px",
                objectFit: "cover",
                margin: "2px 2px",
                borderRadius: "5px",
              }}
              alt=""
            />
          ))}
        </div>
        {/* <h5 style={{ display: "block" }}>{hosteldetails?.hostelName}</h5> */}
        <h5 style={{ display: "block" }}>
          <Link to={`/myhostels/${hosteldetails._id}`}>
            {hosteldetails?.hostelName}
          </Link>
        </h5>

        <h6 style={{ display: "block" }}>{hosteldetails?.wardenName}</h6>
        <p style={{ display: "block" }}>{hosteldetails?.hostelContactNumber}</p>
        <p style={{ display: "block" }}>
          <span
            style={{
              padding: "2px",
              margin: "0px 5px",
            }}
          >
            {hosteldetails?.hostelCity}
          </span>
          <span
            style={{
              padding: "2px",
              margin: "0px 5px",
            }}
          >
            {hosteldetails?.hostelState}
          </span>
          <span
            style={{
              padding: "2px",
              margin: "0px 5px",
            }}
          >
            {hosteldetails?.hostelZipCode}
          </span>
        </p>

        {/* <p>{JSON.stringify(hosteldetails[0])}</p> */}
      </Container>
    </>
  );
}
