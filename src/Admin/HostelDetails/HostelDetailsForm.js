import React from "react";
import { InputWrapper, Input, Textarea, SimpleGrid, Text } from "@mantine/core";
import { IconMail } from "@tabler/icons-react";
import { IconMapPin } from "@tabler/icons-react";
// const HostelDetailsForm = React.memo((props) => HostelDetailsFormComp(props));
function HostelDetailsForm(props) {
  const {
    hostelName,
    setHostelName,
    wardenName,
    setWardenName,
    hostelContactNumber,
    setHoselContactNumber,
    hostelEmail,
    setHostelEmail,
    establishedYear,
    setEstablishedYear,
    noOfFloors,
    setNoOfFloors,
    hostelAddress,
    setHostelAddress,
    hostelLandMark,
    setHostelLandMark,
    hostelCity,
    setHostelCity,
    hostelZipcode,
    setHostelZipcode,
    hostelState,
    setHostelState,
    hostelCountry,
    setHostelCountry,
  } = props;
  console.log("form rendered");
  return (
    <>
      <Text fw="500" style={{ marginTop: "4%", marginBottom: "1%" }}>
        HOSTEL DETAILS:-
      </Text>

      <SimpleGrid
        cols={{ base: 1, sm: 2 }}
        spacing={{ base: 'sm', md: 'md' }}
      >
        <InputWrapper label="Hostel Name" size="md">
          <Input
            placeholder="Please Enter Hostel Name"
            onChange={setHostelName}
            value={hostelName}
            variant=""
            size="md"
            required
            autoComplete="on"
          />
        </InputWrapper>
        <InputWrapper label="Warden Name" size="md">
          <Input
            placeholder="Please Enter Warden Name"
            onChange={setWardenName}
            value={wardenName}
            variant=""
            size="md"
            required
          />
        </InputWrapper>
        <InputWrapper label="Contact Number" size="md">
          <Input
            placeholder="Please Enter Warder Contact Number"
            onChange={setHoselContactNumber}
            value={hostelContactNumber}
            variant=""
            size="md"
            required
            autoComplete="on"
          />
        </InputWrapper>
        <InputWrapper label="Email Address" size="md">
          <Input
            icon={<IconMail />}
            placeholder="Please Enter Email Address"
            onChange={setHostelEmail}
            value={hostelEmail}
            variant=""
            size="md"
            required
          />
        </InputWrapper>
        <InputWrapper label="Established Year" size="md">
          <Input
            placeholder="Please Enter Hostel Established Year"
            onChange={setEstablishedYear}
            value={establishedYear}
            variant=""
            size="md"
            required
          />
        </InputWrapper>
        <InputWrapper label="No of Floors" size="md">
          <Input
            placeholder="Please Enter No Of Floors Of Hostel"
            onChange={setNoOfFloors}
            value={noOfFloors}
            variant=""
            size="md"
            required
          />
        </InputWrapper>
      </SimpleGrid>
      <Text fw="500" style={{ marginTop: "4%", marginBottom: "1%" }}>
        HOSTEL ADDRESS:-
      </Text>
      <Textarea
        icon={<IconMapPin stroke={1} />}
        autoComplete="on"
        placeholder="Please Enter Hostel Address"
        onChange={setHostelAddress}
        value={hostelAddress}
        size="md"
        label="Address"
        required
      />
      <SimpleGrid
        cols={{ base: 1, sm: 2 }}
        spacing={{ base: 'sm', md: 'md' }}
      >
        <InputWrapper label="Landmark" size="md">
          <Input
            placeholder="Please Enter Hostel Landmark"
            onChange={setHostelLandMark}
            value={hostelLandMark}
            variant=""
            size="md"
            required
          />
        </InputWrapper>
        <InputWrapper label="City" size="md">
          <Input
            // icon={<EnvelopeClosedIcon />}
            placeholder="Please Enter Hostel City"
            onChange={setHostelCity}
            value={hostelCity}
            variant=""
            size="md"
            required
          />
        </InputWrapper>
        <InputWrapper label="ZipCode" size="md">
          <Input
            placeholder="Please Enter Hostel Area ZipCode"
            onChange={setHostelZipcode}
            value={hostelZipcode}
            variant=""
            size="md"
            required
          />
        </InputWrapper>
        <InputWrapper label="State" size="md">
          <Input
            placeholder="Please Enter State At which Hostel Located"
            onChange={setHostelState}
            value={hostelState}
            variant=""
            size="md"
            required
          />
        </InputWrapper>
        <InputWrapper label="Country" size="md">
          <Input
            placeholder="Please Enter Country At which Hostel Located"
            onChange={setHostelCountry}
            value={hostelCountry}
            variant=""
            size="md"
            required
          />
        </InputWrapper>
      </SimpleGrid>
    </>
  );
}
export default HostelDetailsForm;
