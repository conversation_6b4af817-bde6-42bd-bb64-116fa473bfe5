import React, { useCallback, useEffect, useState } from "react";
import Maps from "./Maps";
import ImageUpload from "../../Components/ImageUpload";
import beforeunload from "../../Js/beforeunload";
import HostelDetailsForm from "./HostelDetailsForm";
import { useInputState } from "@mantine/hooks";
import { Container, Button } from "@mantine/core";
import { UploadIcon } from "@modulz/radix-icons";
import { AddHostelRequest } from "../../Store/hostels";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import ImageDropZone from "../../Components/ImageDropzone";
import ImageViewer from "../../Components/ImagesViewer";

function HostelDetails() {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const [coords, setCoordss] = useState([]);
  const [hostelName, setHostelName] = useInputState("");
  const [wardenName, setWardenName] = useInputState("");
  const [hostelContactNumber, setHoselContactNumber] = useInputState("");
  const [hostelEmail, setHostelEmail] = useInputState("");
  const [establishedYear, setEstablishedYear] = useInputState("");
  const [noOfFloors, setNoOfFloors] = useInputState("");
  const [hostelImages, setHostelImages] = useState([]);
  const [hostelAddress, setHostelAddress] = useInputState("");
  const [hostelLandMark, setHostelLandMark] = useInputState("");
  const [hostelCity, setHostelCity] = useInputState("");
  const [hostelZipcode, setHostelZipcode] = useInputState("");
  const [hostelState, setHostelState] = useInputState("");
  const [hostelCountry, setHostelCountry] = useInputState("");
  // console.log("HostelDetailsWrapper");
  const data = new FormData();

  const setCoords = useCallback((func) => setCoordss(func), []);
  const setImages = useCallback((func) => setHostelImages(func), []);

  console.log(hostelImages);
  const props = {
    hostelName,
    setHostelName,
    wardenName,
    setWardenName,
    hostelContactNumber,
    setHoselContactNumber,
    hostelEmail,
    setHostelEmail,
    establishedYear,
    setEstablishedYear,
    noOfFloors,
    setNoOfFloors,
    hostelAddress,
    setHostelAddress,
    hostelLandMark,
    setHostelLandMark,
    hostelCity,
    setHostelCity,
    hostelZipcode,
    setHostelZipcode,
    hostelState,
    setHostelState,
    hostelCountry,
    setHostelCountry,
  };

  const hostelFormSubmit = (e) => {
    e.preventDefault();

    data.append("hostelName", hostelName);
    data.append("hostelContactNumber", hostelContactNumber);
    data.append("wardenName", wardenName);
    data.append("establishedYear", establishedYear);
    data.append("noOfFloors", noOfFloors);
    hostelImages.forEach((image) => {
      data.append("hostelImages", image);
    });
    data.append("hostelImages", hostelImages);
    data.append("hostelAddress", hostelAddress);
    data.append("hostelLandMark", hostelLandMark);
    data.append("hostelCity", hostelCity);
    data.append("hostelZipCode", hostelZipcode);
    data.append("hostelState", hostelState);
    data.append("hostelCountry", hostelCountry);
    data.append("coordinates", coords[0]);
    data.append("coordinates", coords[1]);
    data.append("hostelEmail", hostelEmail);
    // dispatching hostelform submit (addHostel action)
    dispatch(AddHostelRequest(data))
      .then(() => navigate("/myhostels"))
      .catch((err) => console.log(err));
  };

  // useEffect(() => {
  //   window.addEventListener("beforeunload", beforeunload, { capture: true });
  //   return () => {
  //     window.removeEventListener("beforeunload", beforeunload, {
  //       capture: true,
  //     });
  //   };
  // }, []);

  return (
    <Container sx={{ marginTop: "4%" }}>
      <form autoComplete="on">
        {console.log("rendered Hosteldetails")}
        <HostelDetailsForm {...props} />
        {/* <ImageUpload setImages={setImages} /> */}
        <ImageDropZone setImages={setImages} />
        <ImageViewer images={hostelImages} />
        <Maps setCoords={setCoords}></Maps>
        <Button
          leftIcon={<UploadIcon />}
          variant="outline"
          style={{ marginTop: "5px" }}
          onClick={hostelFormSubmit}
        >
          Upload
        </Button>
      </form>
    </Container>
  );
}

export default HostelDetails;

// setHostelName={setHostelName}
// // hostelname={hostelName}
// value={(setHostelName, hostelName)}
// const data = {
//   hostelName : hostelName,
//   wardenName : wardenName,
//   hostelContactNumber:hostelContactNumber,
//   hostelEmail:hostelEmail,
//   establishedYear:establishedYear,
//   noOfFloors:noOfFloors,
//   hostelAddress:hostelAddress,
//   hostelLandMark:hostelLandMark,
//   hostelCity:hostelCity,
//   hostelZipcode:hostelZipcode,
// }
// const config = {
//   headers: {
//     'Authorization': 'Bearer ' + sessionStorage.getItem("mysnoutLoginTokin")
//   }
// }
// const url = "https://mysnoutt.herokuapp.com/admin/hosteldetails"

// old handleSubmit code
// const url = "https://mysnoutt.herokuapp.com/admin/hosteldetails";

// const config = {
//   headers: {
//     Authorization: "Bearer " + localStorage.getItem("mysnoutLoginTokin"),
//     "Content-Type": "multipart/form-data",
//   },
// };
// console.log(config);
// const response = fetchData(url, data, config);
// response
//   .then((response) => console.log(response))
//   .catch((error) => console.log(error));

// const instance = axios.create({
//   baseURL: "",
// });
// instance.defaults.headers.common["Authorization"] =
//   "Bearer " + localStorage.getItem("mysnoutLoginTokin");
// instance.defaults.headers.post["Content-Type"] = "multipart/form-data";
// instance.defaults.headers.post["Access-Control-Allow-Methods"] =
//   "GET,PUT,POST,DELETE,PATCH,OPTIONS";
// instance.defaults.headers.post["Access-Control-Allow-Origin"] = "*";
// instance.defaults.headers.post["Access-Control-Allow-Headers"] =
//   "Authorization";

// const response = instance.post(url, data);
// response
//   .then((response) => console.log(response))
//   .catch((error) => console.log(error));
