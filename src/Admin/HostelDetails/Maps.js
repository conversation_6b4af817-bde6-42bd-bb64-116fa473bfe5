import React, { useEffect } from "react";
import "./Maps.css";
const Maps = React.memo((props) => Gmaps(props));
function Gmaps(props) {
  //   let google = window.google;
  console.log(window.google);
  let map;
  let infoWindow;
  let marker;
  let geocoder;
  let responseDiv;
  let response;
  let coordinates = [];
  const loadScript = function (url) {
    let index = window.document.getElementsByTagName("script")[0];
    let script = window.document.createElement("script");
    script.src = url;
    script.async = true;
    script.defer = true;
    index.parentNode.insertBefore(script, index);
  };
  const renderMap = () => {
    // console.log(process.env.GOOGLE_MAPS_URL);
    loadScript(
      "https://maps.googleapis.com/maps/api/js?key=AIzaSyAF-_yM9oifkFWMs97qlCJPOyhIkkA2WyA&callback=initMap&v=weekly"
    );
  };
  useEffect(() => {
    renderMap();
    return () => {
      window.google = undefined;
      let scr = window.document.getElementsByTagName("script");
      for (let i = 0; i < scr.length; i++) {
        scr[i].remove();
      }
      scr[0].remove();
    };
  }, []);
  function initMap() {
    map = new window.google.maps.Map(document.getElementById("map"), {
      zoom: 16,
      center: { lat: -34.397, lng: 150.644 },
      mapTypeControl: false,
    });
    infoWindow = new window.google.maps.InfoWindow();
    geocoder = new window.google.maps.Geocoder();
    marker = new window.google.maps.Marker({
      map,
    });

    const inputText = document.createElement("input");

    inputText.type = "text";
    inputText.placeholder = "Enter a location";

    const submitButton = document.createElement("input");

    submitButton.type = "button";
    submitButton.value = "Search";
    submitButton.classList.add("button", "button-primary");

    const clearButton = document.createElement("input");

    clearButton.type = "button";
    clearButton.value = "Clear";
    clearButton.classList.add("button", "button-secondary");
    response = document.createElement("pre");
    response.id = "response";
    response.innerText = "";
    responseDiv = document.createElement("div");
    responseDiv.id = "response-container";
    responseDiv.appendChild(response);

    const instructionsElement = document.createElement("p");

    instructionsElement.id = "instructions";
    instructionsElement.innerHTML =
      "<strong>Instructions</strong>: Enter an address in the textbox to geocode or click on the map to reverse geocode.";
    map.controls[window.google.maps.ControlPosition.TOP_LEFT].push(inputText);
    map.controls[window.google.maps.ControlPosition.TOP_LEFT].push(
      submitButton
    );
    map.controls[window.google.maps.ControlPosition.TOP_RIGHT].push(
      clearButton
    );
    map.controls[window.google.maps.ControlPosition.LEFT_TOP].push(
      instructionsElement
    );
    map.controls[window.google.maps.ControlPosition.LEFT_TOP].push(responseDiv);
    const locationButton = document.createElement("button");

    locationButton.textContent = "pan to current location";
    locationButton.classList.add("custom-map-control-button");
    map.controls[window.google.maps.ControlPosition.TOP_RIGHT].push(
      locationButton
    );
    locationButton.addEventListener("click", (e) => {
      // Try HTML5 geolocation.
      e.preventDefault();
      if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            const pos = {
              lat: position.coords.latitude,
              lng: position.coords.longitude,
            };
            // coordinates.push(String(position.coords.latitude));
            // coordinates.push(String(position.coords.longitude));
            coordinates = [
              String(position.coords.latitude),
              String(position.coords.longitude),
            ];
            infoWindow.setPosition(pos);
            infoWindow.setContent("Location found.");
            infoWindow.open(map);
            map.setCenter(pos);
            map.setZoom(16);
            props.setCoords(coordinates);
          },
          () => {
            handleLocationError(true, infoWindow, map.getCenter());
          }
        );
      } else {
        // Browser doesn't support Geolocation
        handleLocationError(false, infoWindow, map.getCenter());
      }
    });
    map.addListener("click", (e) => {
      geocode({ location: e.latLng });
    });
    submitButton.addEventListener("click", () =>
      geocode({ address: inputText.value })
    );
    clearButton.addEventListener("click", () => {
      clear();
    });
    clear();
  }
  function clear() {
    marker.setMap(null);
    responseDiv.style.display = "none";
  }

  function geocode(request) {
    clear();
    geocoder
      .geocode(request)
      .then((result) => {
        const { results } = result;
        map.setCenter(results[0].geometry.location);
        marker.setPosition(results[0].geometry.location);
        marker.setMap(map);
        responseDiv.style.display = "block";
        response.innerText = JSON.stringify(result, null, 2);
        return results;
      })
      .catch((e) => {
        alert("Geocode was not successful for the following reason: " + e);
      });
  }
  function handleLocationError(browserHasGeolocation, infoWindow, pos) {
    infoWindow.setPosition(pos);
    infoWindow.setContent(
      browserHasGeolocation
        ? "Error: The Geolocation service failed."
        : "Error: Your browser doesn't support geolocation."
    );
    infoWindow.open(map);
  }
  if (!window.google) {
    window.initMap = initMap;
  }
  return (
    <>
      <div id="map"></div>
      {console.log("Maps rendered")}
    </>
  );
}
export default Maps;
