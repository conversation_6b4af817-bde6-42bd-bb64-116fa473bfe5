import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import HostelCard from "../../Components/HostelCard/HostelCard";
import { GetAllHostels } from "../../Store/hostels";
import { Container, SimpleGrid } from "@mantine/core";
function MyHostels() {
  const dispatch = useDispatch();
  const { hosteldetails, isFetched } = useSelector((store) => ({
    hosteldetails: store.hostels.hostelsList,
    isFetched: store.hostels.isFetched,
  }));

  // console.log(count, hosteldetails);
  // console.log("hosteldetails");

  useEffect(() => {
    if (Object.keys(hosteldetails).length === 0 || !isFetched) {
      dispatch(GetAllHostels()).catch((err) => console.log(err));
    }
    console.log("hii");
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      <Container p={0}>
        <SimpleGrid
          cols={2}
          breakpoints={[
            { maxWidth: 980, cols: 2, spacing: "md" },
            { maxWidth: 750, cols: 1, spacing: "sm" },
            { maxWidth: 600, cols: 1, spacing: "sm" },
          ]}
        >
          {Object.keys(hosteldetails).length !== 0 &&
            Object.keys(hosteldetails).map((key) => (
              <HostelCard key={key} hostel={hosteldetails[key]} />
            ))}
        </SimpleGrid>
      </Container>
    </>
  );
}

export default MyHostels;

// old code
// let [hosteldetails, sethosteldetails] = useState(null);

// useEffect(() => {
//   const config = {
//     headers: {
//       Authorization: "Bearer " + authToken,
//     },
//   };
//   const response = GetData(
//     "https://mysnoutt.herokuapp.com/admin/hosteldetails",
//     config
//   );
//   response
//     .then((response) => {
//       console.log(response);
//       sethosteldetails(response.hostelDetails);
//       dispatch(hydrateHostel(response));
//     })
//     .catch((error) => console.log(error));
// }, [authToken]);
// const [count, setCount] = useState(0);      <button onClick={() => setCount(count + 1)}>inc</button>
