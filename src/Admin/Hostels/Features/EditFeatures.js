import EditFeaturesForm from "./EditFeaturesForm";
export default function EditFeatures({ setEditing, data }) {
  const features = {};
  Object.keys(data).map((item) =>
    typeof data[item] === "object"
      ? (features[item] = data[item].map((feature) => feature["_id"]))
      : ""
  );
  //   console.log(features);
  return (
    <>
      <EditFeaturesForm allFeatures={features} setEditing={setEditing} />
    </>
  );
}

// export default function FeatureTypeChips() {

//   return (
//     <div key={`${Type.belongsTo} + "div"`}>
//       <Text key={"12345" + Type.belongsTo}>{Type.belongsTo}</Text>
//       <Group position="apart" mt={2} mb={5}>
//         <Chips
//           key={Type.belongsTo}
//           multiple
//           color="violet"
//           variant="filled"
//           onChange={setValue}
//           size={matches ? "sm" : "xs"}
//           // sx={{
//           //   "&:disabled": {
//           //     color: `aqua !important`,
//           //   },
//           // }}
//           styles={{
//             disabled: {
//               background: `none !important`,
//             },
//           }}
//         >
//           {Type.features.map((feature) => (
//             <Chip checked key={feature.id} value={feature.id}>
//               {feature.feature}
//             </Chip>
//           ))}
//         </Chips>
//       </Group>
//     </div>
//   );
// }
