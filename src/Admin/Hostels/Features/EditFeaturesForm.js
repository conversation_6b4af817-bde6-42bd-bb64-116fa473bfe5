import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import {
  GetFeatures,
  UpdateHostelFeatures,
} from "../../../Store/hostelFeatures";
import { ChipGroup, Chip, Button, Text, Group } from "@mantine/core";

import { useParams } from "react-router-dom";
import { useMediaQuery } from "@mantine/hooks";
/*
 * this function renders all the types of features that we get from the server on mounting -> useEffect
 *
 */

export default function EditFeaturesForm({ allFeatures, setEditing }) {
  const dispatch = useDispatch();
  // const { height, width } = useViewportSize();

  const matches = useMediaQuery("(min-width: 381.7px)");
  // console.log(matches);
  const { hid } = useParams();
  const [features, setFeatures] = useState(null);
  const [charges, setCharges] = useState([...allFeatures.charges]);
  const [cleaningServices, setCleaningServics] = useState(
    allFeatures.cleaningServices
  );
  const [homeRules, setHomeRules] = useState(allFeatures.homeRules);
  const [hostelAvailableFor, setHostelAvailableFor] = useState(
    allFeatures.hostelAvailableFor
  );
  const [hostelPrefferedFor, setHostelPrefferedFor] = useState(
    allFeatures.hostelPrefferedFor
  );
  const [security, setSecurity] = useState(allFeatures.security);
  const [amenities, setAmenities] = useState(allFeatures.amenities);
  const [facilities, setFacilities] = useState(allFeatures.facilities);
  // console.log(
  //   charges,
  //   cleaningServices,
  //   homeRules,
  //   hostelAvailableFor,
  //   hostelPrefferedFor,
  //   security,
  //   amenities,
  //   facilities
  // );
  // console.log(allFeatures, charges);
  console.log(hostelPrefferedFor);
  const onChangeMethod = (Type) => {
    let setValue;
    let defValues = [];
    if (Type.belongsTo === "Additional charges") {
      setValue = setCharges;
      defValues = allFeatures.charges;
    } else if (Type.belongsTo === "Cleaning services") {
      setValue = setCleaningServics;
      defValues = allFeatures.cleaningServices;
    } else if (Type.belongsTo === "Home rules") {
      setValue = setHomeRules;
      defValues = allFeatures.homeRules;
    } else if (Type.belongsTo === "Hostel available for") {
      setValue = setHostelAvailableFor;
      defValues = allFeatures.hostelAvailableFor;
    } else if (Type.belongsTo === "Hostel preferred for") {
      setValue = setHostelPrefferedFor;
      defValues = allFeatures.hostelPrefferedFor;
    } else if (Type.belongsTo === "Security") {
      setValue = setSecurity;
      defValues = allFeatures.security;
    } else if (Type.belongsTo === "amenities") {
      setValue = setAmenities;
      defValues = allFeatures.amenities;
    } else if (Type.belongsTo === "facilities") {
      setValue = setFacilities;
      defValues = allFeatures.facilities;
    }
    return [setValue, defValues];
  };
  const handleSubmit = () => {
    const data = {
      charges,
      cleaningServices,
      homeRules,
      hostelAvailableFor,
      hostelPrefferedFor,
      security,
      amenities,
      facilities,
    };
    dispatch(UpdateHostelFeatures(data, hid)).then(() => setEditing());
  };

  useEffect(() => {
    dispatch(GetFeatures()).then((res) => {
      setFeatures(res.features);
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  //
  return (
    <>
      <h1>Features</h1>
      {/* to check if features is not null so it wont map to a null and render  */}
      {features &&
        features.map((Type) => {
          let [setValue, defValues] = onChangeMethod(Type);
          return (
            <div key={`${Type.belongsTo} + "div"`}>
              <Text key={"12345" + Type.belongsTo}>{Type.belongsTo}</Text>
              <Group position="apart" mt={2} mb={5}>
                <ChipGroup
                  key={Type.belongsTo}
                  multiple
                  color="violet"
                  variant="filled"
                  defaultValue={defValues}
                  onChange={setValue}
                  size={matches ? "sm" : "xs"}
                  // sx={{
                  //   "&:disabled": {
                  //     color: `aqua !important`,
                  //   },
                  // }}
                  styles={{
                    disabled: {
                      background: `none !important`,
                    },
                  }}
                >
                  {Type.features.map((feature) => (
                    <Chip key={feature.id} value={feature.id}>
                      {feature.feature}
                    </Chip>
                  ))}
                </ChipGroup>
              </Group>
            </div>
          );
        })}
      {features && (
        <Group position="right">
          <Button variant="light" onClick={() => setEditing()} color={"red"}>
            Cancel
          </Button>
          <Button variant="light" color={"green"} onClick={handleSubmit}>
            Save
          </Button>
        </Group>
      )}
    </>
  );
}
// {/* Additional charges */}
// {/* Cleaning services */}
// {/* Home rules */}
// {/* Hostel available for */}
// {/* Hostel preferred for */}
// {/* Security */}
// {/* amenities */}
// {/* facilities */}

// features.map((Type) => {
// let setValue = onChangeMethod(Type);
// return (
//   <div key={`${Type.belongsTo} + "div"`}>
//     <Text key={"12345" + Type.belongsTo}>{Type.belongsTo}</Text>
//     <Group position="apart" mt={2} mb={5}>
//       <Grid.Col key={"grid" + Type.belongsTo} span={4}>
//         <Chips
//           key={Type.belongsTo}
//           multiple
//           color="violet"
//           variant="filled"
//           onChange={setValue}
//         >
//           {Type.features.map((feature) => (
//             <Chip key={feature.id} value={feature.id}>
//               {feature.feature}
//             </Chip>
//           ))}
//         </Chips>
//       </Grid.Col>
//     </Group>
//   </div>
//   );
// })}

// {/* <Grid key={`${Type.belongsTo} + "div"`}>
// {/* <Grid.Col span={12}>
//   <Text key={"12345" + Type.belongsTo}>{Type.belongsTo}</Text>
// </Grid.Col> */}

// <Chips
//   key={Type.belongsTo}
//   multiple
//   color="violet"
//   variant="filled"
//   onChange={setValue}
// >
//   <Grid.Col
//     key={"grid" + Type.belongsTo}
//     sm={12}
//     xs={6}
//     md={6}
//     lg={6}
//   >
//     {Type.features.map((feature) => (
//       <Chip key={feature.id} value={feature.id}>
//         {feature.feature}
//       </Chip>
//     ))}
//   </Grid.Col>
// </Chips>
// </Grid> */}

// let setValue = onChangeMethod(Type);
// return (
//   <>
//     <Grid key={"grid" + Type.belongsTo}>
//       <Chips
//         key={Type.belongsTo}
//         multiple
//         color="violet"
//         variant="filled"
//         onChange={setValue}
//         size="sm"
//       >
//         {Type.features.map((feature) => (
//           <Grid.Col key={"grid" + feature.id} span={4}>
//             <Chip key={feature.id} value={feature.id}>
//               {feature.feature}
//             </Chip>
//           </Grid.Col>
//         ))}
//       </Chips>
//     </Grid>
