import React, { useEffect } from "react";
import NoFeaturesAdded from "./NoFeaturesAdded";
import { useParams } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { GetHostelFeatures } from "../../../Store/hostelFeatures";
import ShowFeatures from "./ShowFeatures";
import { useToggle } from "@mantine/hooks";
import EditFeatures from "./EditFeatures";

export default function Features() {
  const [isEditing, setEditing] = useToggle(false, [true, false]);
  // console.log(opened);

  const params = useParams();
  const dispatch = useDispatch();
  const hid = params.hid;
  const hostelFeatures = useSelector(
    (store) => store.hostelFeatures?.features[hid]
  );

  useEffect(() => {
    if (!hostelFeatures) dispatch(GetHostelFeatures(hid));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // console.log(params, hostelFeatures);

  return (
    <>
      {hostelFeatures ? (
        isEditing ? (
          <EditFeatures data={hostelFeatures} setEditing={setEditing} />
        ) : (
          <ShowFeatures data={hostelFeatures} setEditing={setEditing} />
        )
      ) : (
        <NoFeaturesAdded />
      )}
    </>
  );
}

// {opened ? (
//   <Modal
//     opened={opened}
//     onClose={() => setOpened(false)}
//     title="Hostel Features!"
//     overlayColor={
//       theme.colorScheme === "dark"
//         ? theme.colors.dark[9]
//         : theme.colors.gray[2]
//     }
//     overlayOpacity={0.55}
//     overlayBlur={3}
//   >
//     <AddFeaturesForm />
//   </Modal>
// ) : (
//   ""
// )}
