import { Text, Paper, Group, Button } from "@mantine/core";
import { useToggle } from "@mantine/hooks";
import AddFeaturesForm from "./AddFeaturesForm";

export default function NoFeaturesAdded() {
  const [opened, setOpened] = useToggle(false, [true, false]);
  const NoFeaturesHTML = (
    <Paper
      style={{ textAlign: "center" }}
      shadow="sm"
      radius="md"
      p="lg"
      withBorder
    >
      <Text fw="bold" size="lg">
        You haven't added any features for your hostel yet!
      </Text>
      <Text size="xs" color="dimmed">
        Use the below add features button to add the features
      </Text>
      <Group justify="center" mt="sm">
        <Button variant="light" onClick={() => setOpened()}>
          Add Features
        </Button>
      </Group>
    </Paper>
  );
  return <>{opened ? <AddFeaturesForm /> : NoFeaturesHTML}</>;
}
