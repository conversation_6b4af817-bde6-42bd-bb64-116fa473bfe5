import { Chip<PERSON><PERSON>, <PERSON>, But<PERSON>, Text, Group } from "@mantine/core";
import { useDispatch } from "react-redux";
import { GetFeatures } from "../../../Store/hostelFeatures";
import { useMediaQuery } from "@mantine/hooks";
import { useEffect, useState } from "react";

const availableFeaturesDictionary = (data) => {
  const dict = {};
  Object.keys(data).map((Type) =>
    typeof data[Type] === "object"
      ? data[Type]?.map((feature) => (dict[feature["_id"]] = "true"))
      : ""
  );
  return dict;
};
export default function ShowFeatures({ data, setEditing }) {
  const matches = useMediaQuery("(min-width: 381.7px)");
  const dispatch = useDispatch();
  const [features, setFeatures] = useState(null);
  const allAvailableFeatures = availableFeaturesDictionary(data);
  // console.log(data);
  useEffect(() => {
    dispatch(GetFeatures()).then((res) => {
      setFeatures(res.features);
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  // Strategy Pattern: Map feature types to data keys
  const FEATURE_TYPE_MAPPING = {
    "Additional charges": "charges",
    "Cleaning services": "cleaningServices",
    "Home rules": "homeRules",
    "Hostel available for": "hostelAvailableFor",
    "Hostel preferred for": "hostelPrefferedFor",
    "Security": "security",
    "amenities": "amenities",
    "facilities": "facilities"
  };

  const selectedFeatures = (Type) => {
    const dataKey = FEATURE_TYPE_MAPPING[Type.belongsTo];
    return dataKey ? data[dataKey]?.map((feature) => feature["_id"]) : [];
  };
  return (
    <>
      {features && (
        <div>
          {features.map((Type) => {
            let values = selectedFeatures(Type);
            return (
              <div key={`${Type.belongsTo} + "div"`}>
                <Text key={"12345" + Type.belongsTo}>{Type.belongsTo}</Text>
                <Group justify="space-between" mt={2} mb={5}>
                  <ChipGroup
                    key={Type.belongsTo}
                    multiple
                    color="green"
                    variant="filled"
                    value={values}
                    size={matches ? "sm" : "xs"}
                    // sx={{
                    //   "&:disabled": {
                    //     color: `aqua !important`,
                    //   },
                    // }}
                    styles={{
                      disabled: {
                        background: `none !important`,
                      },
                    }}
                  >
                    {Type.features.map((feature) => (
                      <Chip
                        key={feature.id}
                        disabled={
                          allAvailableFeatures[feature.id] ? false : true
                        }
                        value={feature.id}
                      >
                        {feature.feature}
                      </Chip>
                    ))}
                  </ChipGroup>
                </Group>
              </div>
            );
          })}
          <Group justify="flex-end">
            <Button variant="light" uppercase onClick={() => setEditing()}>
              Edit
            </Button>
          </Group>
        </div>
      )}
    </>
  );
}
