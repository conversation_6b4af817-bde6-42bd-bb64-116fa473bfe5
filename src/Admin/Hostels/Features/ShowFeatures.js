import { <PERSON>s, Chip, Button, Text, Paper, Group, Grid } from "@mantine/core";
import { useDispatch } from "react-redux";
import { GetFeatures } from "../../../Store/hostelFeatures";
import { useMediaQuery } from "@mantine/hooks";
import { useEffect, useState } from "react";

const availableFeaturesDictionary = (data) => {
  const dict = {};
  Object.keys(data).map((Type) =>
    typeof data[Type] === "object"
      ? data[Type]?.map((feature) => (dict[feature["_id"]] = "true"))
      : ""
  );
  return dict;
};
export default function ShowFeatures({ data, setEditing }) {
  const matches = useMediaQuery("(min-width: 381.7px)");
  const dispatch = useDispatch();
  const [features, setFeatures] = useState(null);
  const allAvailableFeatures = availableFeaturesDictionary(data);
  // console.log(data);
  useEffect(() => {
    dispatch(GetFeatures()).then((res) => {
      setFeatures(res.features);
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  const selectedFeatures = (Type) => {
    let setValue;

    if (Type.belongsTo === "Additional charges") {
      setValue = data["charges"]?.map((feature) => feature["_id"]);
    } else if (Type.belongsTo === "Cleaning services") {
      setValue = data["cleaningServices"]?.map((feature) => feature["_id"]);
    } else if (Type.belongsTo === "Home rules") {
      setValue = data["homeRules"]?.map((feature) => feature["_id"]);
    } else if (Type.belongsTo === "Hostel available for") {
      setValue = data["hostelAvailableFor"]?.map((feature) => feature["_id"]);
    } else if (Type.belongsTo === "Hostel preferred for") {
      setValue = data["hostelPrefferedFor"]?.map((feature) => feature["_id"]);
    } else if (Type.belongsTo === "Security") {
      setValue = data["security"]?.map((feature) => feature["_id"]);
    } else if (Type.belongsTo === "amenities") {
      setValue = data["amenities"]?.map((feature) => feature["_id"]);
    } else if (Type.belongsTo === "facilities") {
      setValue = data["facilities"]?.map((feature) => feature["_id"]);
    }
    return setValue;
  };
  return (
    <>
      {features && (
        <div>
          {features.map((Type) => {
            let values = selectedFeatures(Type);
            return (
              <div key={`${Type.belongsTo} + "div"`}>
                <Text key={"12345" + Type.belongsTo}>{Type.belongsTo}</Text>
                <Group position="apart" mt={2} mb={5}>
                  <Chips
                    key={Type.belongsTo}
                    multiple
                    color="green"
                    variant="filled"
                    value={values}
                    size={matches ? "sm" : "xs"}
                    // sx={{
                    //   "&:disabled": {
                    //     color: `aqua !important`,
                    //   },
                    // }}
                    styles={{
                      disabled: {
                        background: `none !important`,
                      },
                    }}
                  >
                    {Type.features.map((feature) => (
                      <Chip
                        key={feature.id}
                        disabled={
                          allAvailableFeatures[feature.id] ? false : true
                        }
                        value={feature.id}
                      >
                        {feature.feature}
                      </Chip>
                    ))}
                  </Chips>
                </Group>
              </div>
            );
          })}
          <Group position="right">
            <Button variant="light" uppercase onClick={() => setEditing()}>
              Edit
            </Button>
          </Group>
        </div>
      )}
    </>
  );
}
