/**
 * Feature Type Constants and Utilities
 * 
 * This file centralizes all feature type mappings and provides utility functions
 * to eliminate if-else ladders throughout the feature components.
 */

// Feature type to data key mapping
export const FEATURE_TYPE_TO_DATA_KEY = {
  "Additional charges": "charges",
  "Cleaning services": "cleaningServices",
  "Home rules": "homeRules", 
  "Hostel available for": "hostelAvailableFor",
  "Hostel preferred for": "hostelPrefferedFor",
  "Security": "security",
  "amenities": "amenities",
  "facilities": "facilities"
};

// Utility function to get data key from feature type
export const getDataKeyFromFeatureType = (featureType) => {
  return FEATURE_TYPE_TO_DATA_KEY[featureType];
};

// Utility function to extract feature IDs from data
export const extractFeatureIds = (data, featureType) => {
  const dataKey = getDataKeyFromFeatureType(featureType);
  return dataKey ? data[dataKey]?.map((feature) => feature["_id"]) || [] : [];
};

// Utility function to get feature setter configuration
export const createFeatureSetterConfig = (setters) => {
  const config = {};
  Object.keys(FEATURE_TYPE_TO_DATA_KEY).forEach(featureType => {
    const dataKey = FEATURE_TYPE_TO_DATA_KEY[featureType];
    config[featureType] = {
      setter: setters[dataKey],
      dataKey: dataKey
    };
  });
  return config;
};

// Utility function to create simple setter mapping
export const createFeatureSetterMapping = (setters) => {
  const mapping = {};
  Object.keys(FEATURE_TYPE_TO_DATA_KEY).forEach(featureType => {
    const dataKey = FEATURE_TYPE_TO_DATA_KEY[featureType];
    mapping[featureType] = setters[dataKey];
  });
  return mapping;
};

/**
 * Example usage:
 * 
 * // In ShowFeatures.js
 * const selectedFeatures = (Type) => {
 *   return extractFeatureIds(data, Type.belongsTo);
 * };
 * 
 * // In AddFeaturesForm.js
 * const setters = { charges: setCharges, cleaningServices: setCleaningServics, ... };
 * const FEATURE_SETTERS = createFeatureSetterMapping(setters);
 * const onChangeMethod = (Type) => FEATURE_SETTERS[Type.belongsTo] || null;
 * 
 * // In EditFeaturesForm.js
 * const setters = { charges: setCharges, cleaningServices: setCleaningServics, ... };
 * const FEATURE_CONFIG = createFeatureSetterConfig(setters);
 * const onChangeMethod = (Type) => {
 *   const config = FEATURE_CONFIG[Type.belongsTo];
 *   return config ? [config.setter, allFeatures[config.dataKey] || []] : [null, []];
 * };
 */
