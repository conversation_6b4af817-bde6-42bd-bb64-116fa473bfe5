import { Tabs } from "@mantine/core";
import MyRooms from "../../Rooms/MyRooms";
import Hostel from "../../HostelDetails/Hostel";
import Features from "../Features/Features";
import { useNavigate } from "react-router-dom";
import RoomsWrapper from "../../Rooms/RoomsWrapper";

export default function HostelTabs(props) {
  const navigate = useNavigate();

  const tabNavigationController = (tabIndex) => {
    if (tabIndex === 0) {
      navigate("../");
    } else if (tabIndex === 1) {
      if (props.initialTab === 0) navigate("features");
      else navigate("../features");
    } else if (tabIndex === 2) {
      if (props.initialTab === 0) navigate("rooms");
      else navigate("../rooms");
    }
  };

  return (
    <Tabs
      color="violet"
      tabPadding="md"
      onTabChange={tabNavigationController}
      initialTab={props.initialTab}
    >
      <Tabs.Tab label="Hostel Details">
        <Hostel />
      </Tabs.Tab>

      <Tabs.Tab label="Hostel Features">
        <Features />
      </Tabs.Tab>

      <Tabs.Tab label="Rooms">
        <RoomsWrapper />
      </Tabs.Tab>
    </Tabs>
  );
}
