import React, { useState } from "react";
import { loginReq } from "../../Store/auth";
import { useNavigate, Navigate } from "react-router-dom";
import { useForm } from "@mantine/form";
import { useDispatch, useSelector } from "react-redux";
import PasswordInputLocal from "../../Components/PasswordInputLocal";
import { Loader } from "@mantine/core";
import {
  Input,
  Checkbox,
  Anchor,
  Paper,
  Title,
  Text,
  Container,
  Group,
  Button,
  InputWrapper,

} from "@mantine/core";

// todo 1. add email and password validations
// check useform hook validations not working for PasswordinputLocal component
// try to find good background for login page

const styles = {
  paper: {
    padding: "30px",
    marginTop: "30px",
    maxWidth: "432px",
  },
  Container: {
    marginLeft: "auto",
    marginRight: "auto",
  },
};
function Login() {
  const navigate = useNavigate();
  // const [email, setEmail] = useState("");
  // const [pass, setPass] = useState("");

  const form = useForm({
    initialValues: {
      email: "",
      pass: "",
    },

    validate: {
      email: (value) => (/^\S+@\S+$/.test(value) ? null : "Invalid email"),
    },
  });

  const dispatch = useDispatch();
  const isLoggedIn = useSelector((state) => state.auth.isLoggedIn);

  /*
  ~ When login is clicked, isLogging in is used to
  ~ 1) to disable the form
  ~ 2) to set the dots-loader to true in the login button
  ~ 3) to change button value from Login to Logging in
   */
  const [isLoggingin, setLoggingIn] = useState(false);

  // const [isVerified, setVerified] = useState(false)
  // const [isLoggedIn,setLogin] = useState(false)
  // useRef makes component uncontrolled uncontrolled. In a controlled component, form data is handled by a React component. The alternative is uncontrolled components, where form data is handled by the DOM itself.
  // To write an uncontrolled component, instead of writing an event handler for every state update, you can use a ref to get form values from the DOM.
  // const emailAddress = useRef();
  // const password = useRef();

  const handleSubmit = (values) => {
    // e.preventDefault();
    setLoggingIn(true);
    console.log(values);
    dispatch(loginReq(values.email, values.pass))
      .then(() => {
        setLoggingIn(false);
        navigate("/");
      })
      .catch((err) => {
        console.log(err);
        setLoggingIn(false);
      });
  };

  if (isLoggedIn) {
    return <Navigate to="/" />;
  }

  return (
    <>
      <div>
        <Container style={styles.Container} size={464} my={40}>
          <Title
            align="center"
            sx={(theme) => ({
              fontFamily: `Greycliff CF, ${theme.fontFamily}`,
              fontWeight: 900,
            })}
          >
            Welcome back!
          </Title>
          <Text
            color="dimmed"
            // variant="gradient"
            // gradient={{ from: "teal", to: "violet", deg: 45 }}
            weight={600}
            size="md"
            align="center"
            mt={5}
          >
            Do not have an account yet?{" "}
            <Anchor size="sm" onClick={() => navigate("/signup")}>
              Create account
            </Anchor>
          </Text>
          <Paper style={styles.paper} withBorder radius="md">
            <InputWrapper label="Email">
              <Input
                type=""
                placeholder="<EMAIL>"
                variant="default"
                {...form.getInputProps("email")}
                disabled={isLoggingin}
              />
            </InputWrapper>
            <PasswordInputLocal
              label="Password"
              placeholder="Your password"
              variant="default"
              mt="md"
              {...form.getInputProps("pass")}
              disabled={isLoggingin}
              autoComplete="off"
            />
            {/* <PasswordInput
            label="Password"
            placeholder="Your password"
            required
            mt="md"
            variant="default"
            {...form.getInputProps("pass")}
          /> */}
            <Group position="apart" mt="md">
              <Checkbox label="Remember me" />
              <Anchor
                onClick={() => navigate("/forgotpassword")}
                href="#"
                size="sm"
              >
                Forgot password?
              </Anchor>
            </Group>
            <Group position="right">
              <Button
                fullWidth
                // sx={{
                //   "&:disabled": {
                //     backgroundColor: `white !important`,
                //   },
                // }}
                mt="xl"
                color="violet"
                onClick={form.onSubmit(handleSubmit)}
                disabled={isLoggingin}
                rightIcon={
                  isLoggingin ? (
                    <Loader size="24" color="teal" variant="dots" />
                  ) : (
                    ""
                  )
                }
              >
                {isLoggingin ? "Logging In" : "Login"}
                {/* <Loader size="32" color="teal" variant="dots" /> */}
              </Button>
            </Group>
          </Paper>
        </Container>
      </div>
    </>
  );
}

export default Login;

//
//
//
//

// old bootstrap code
// eslint-disable-next-line no-lone-blocks
//
//
// <form
//         style={{ width: 720 }}
//         className="container"
//         onSubmit={handleSubmit}
//       >
//         <div className="mb-3">
//           <label htmlFor="exampleInputEmail1" className="form-label">
//             Email address
//           </label>
//           <input
//             type="email"
//             onChange={handleEvent}
//             value={email}
//             name="emailAddress"
//             className="form-control"
//             id="exampleInputEmail1"
//             aria-describedby="emailHelp"
//           />
//           <div id="emailHelp" className="form-text">
//             We'll never share your email with anyone else.
//           </div>
//         </div>
//         <div className="mb-3">
//           <label htmlFor="exampleInputPassword1" className="form-label">
//             Password
//           </label>
//           <input
//             type="password"
//             onChange={handleEvent}
//             value={pass}
//             name="password"
//             className="form-control"
//             id="exampleInputPassword1"
//           />
//         </div>
//         <div className="mb-3 form-check">
//           <input
//             type="checkbox"
//             className="form-check-input"
//             id="exampleCheck1"
//           />
//           <label className="form-check-label" htmlFor="exampleCheck1">
//             Check me out
//           </label>
//         </div>
//         <Link style={{ float: "right" }} to="/forgotpassword">
//           Forgot Password?
//         </Link>{" "}
//         <button type="submit" className="btnPP btn-primary">
//           Submit
//         </button>
//       </form>

//       const handleEvent = (e) => {
//     if (e.currentTarget.name === "emailAddress") {
//       setEmail(e.currentTarget.value);
//     } else {
//       setPass(e.currentTarget.value);
//     }
//   };
//
// }
