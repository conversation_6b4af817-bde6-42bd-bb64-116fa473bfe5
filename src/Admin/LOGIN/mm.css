* {
    box-sizing: border-box;
  }
  
  .background-image {
    background-image: url('https://cdn.pixabay.com/photo/2015/04/23/22/00/tree-736885__480.jpg');
    background-size: cover;    
    filter: blur(5px);

    display: block;
    /* -webkit-filter: blur(5px); */
    height: 100vh;
    left: 0;
    position: fixed;
    right: 0;
    z-index: 1;
  }
  
  .content {
    /* background: rgba(255, 255, 255, 0.35); */
    top: 10px;
    left: 0;
    position: fixed;
    right: 0;
    z-index: 2;
  }