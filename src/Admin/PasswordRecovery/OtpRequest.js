import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import fetchData from "../../Js/FetchData";
import {
  Paper,
  Title,
  Text,
  Button,
  Container,
  Group,
  Anchor,
  Center,
  Box,
  Input,
  InputWrapper,
} from "@mantine/core";
import { IconArrowLeft } from "@tabler/icons-react";

const styles = {
  title: {
    fontSize: 26,
    fontWeight: 900,
    fontFamily: 'Greycliff CF, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif',
  },
  controls: {
    display: 'flex',
    gap: '1rem',
  },
  control: {
    flex: 1,
  },
};

function OtpRequest() {
  const [emailadd, setEmail] = useState("");
  const navigate = useNavigate();

  const SentOtp = async (e) => {
    e.preventDefault();

    const response = await fetchData(
      "https://mysnoutt.herokuapp.com/admin/forgotPassword",
      { email: emailadd }
    );

    console.log(response);

    navigate("/forgotpassword/verifyotp", {
      state: { adminId: response.adminId },
    });
  };

  const handleChange = (e) => {
    setEmail(e.currentTarget.value);
  };

  return (
    <>
      <Container size={460} my={30}>
        <Title style={styles.title} align="center">
          Forgot your password?
        </Title>
        <Text color="dimmed" size="sm" align="center">
          Enter your email to get a reset link.
        </Text>

        <form>
          <Paper withBorder shadow="md" p={30} radius="md" mt="xl">
            <InputWrapper label="Your email" requried="true">
              <Input placeholder="<EMAIL>" onChange={handleChange} />
            </InputWrapper>

            <Group position="apart" mt="lg" style={styles.controls}>
              <Anchor
                color="dimmed"
                size="sm"
                style={styles.control}
                onClick={() => navigate("/login")}
              >
                <Center inline>
                  <IconArrowLeft size={12} />
                  <Box ml={5}>Back to login page</Box>
                </Center>
              </Anchor>
              <Button style={styles.control} onClick={SentOtp}>
                Send Otp
              </Button>
            </Group>
          </Paper>
        </form>
      </Container>
    </>
  );
}
export default OtpRequest;

// old code
/*
   <form onSubmit={SentOtp}>
        <input
          onChange={handleChange}
          type="email"
          id=""
          placeholder="Enter your email address"
          value={emailadd}
        />
        <button type="submit">Send OTP</button>
      </form> 
*/
