import React, { useState } from "react";
import fetchData from "../../Js/FetchData";
import { useLocation, useNavigate, Navigate } from "react-router-dom";
// import { useForm } from "@mantine/hooks";
import {
  Paper,
  Title,
  Text,
  Button,
  Container,
  Group,
  Anchor,
  Center,
  Box,
  Input,
  InputWrapper,
} from "@mantine/core";
import { useStyles } from "./commonStyles";

export default function OtpVerification() {
  const [otp, setOtp] = useState("");
  const location = useLocation();
  console.log(location);
  const navigate = useNavigate();
  const { classes } = useStyles();

  const adminId = location.state?.adminId;
  if (!adminId) return <Navigate to={"/"} />;
  const otpUrl =
    "https://maheshvattem2.herokuapp.com/admin/verifyOtp/" + adminId;

  const handleChange = (e) => {
    setOtp(e.currentTarget.value);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    const response = await fetchData(otpUrl, { otp: otp });

    if (response.success === 1) {
      navigate("/forgotpassword/reset", {
        state: { adminId: adminId },
      });
    }

    // navigate("/forgotPassword/reset",{state : { adminId: location.state?.adminId}}); ,,, err.response?.data
  };

  return (
    // <div>
    //   <form onSubmit={handleSubmit}>
    //     <input type="text" value={otp} onChange={handleChange}></input>
    //     <input type="submit"></input>
    //   </form>
    // </div>
    <Container size={460} my={30}>
      <Title className={classes.title} align="center">
        Otp Verification
      </Title>
      <Text color="dimmed" size="sm" align="center">
        Enter Otp send to your registered email to reset your password
      </Text>

      <Paper withBorder shadow="md" p={30} radius="md" mt="xl">
        <InputWrapper label="Otp" required>
          <Input placeholder="Enter code" onChange={handleChange} />
        </InputWrapper>

        <Group position="apart" mt="lg" className={classes.controls}>
          <Anchor color="dimmed" size="sm" className={classes.control}>
            <Center inline>
              {/* <ArrowLeftIcon size={12} /> */}
              <Box ml={5}>Resent Otp</Box>
            </Center>
          </Anchor>
          <Button className={classes.control} onClick={handleSubmit}>
            Verify Otp
          </Button>
        </Group>
      </Paper>
    </Container>
  );
}

//np1 - new password, np2 - Confirm new password

// old code
// {/* <form onSubmit={handleSubmit}>
// <div>
//   <label style={{ display: "block" }} htmlFor="np1">
//     New password
//   </label>
//   <input
//     style={{ width: 250 }}
//     type="password"
//     id="np1"
//     onChange={handlepass1}
//     value={pass1}
//     placeholder="Enter your new password"
//     required
//   />
// </div>
