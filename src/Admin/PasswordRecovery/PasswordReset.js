import React from "react";
import { useLocation, useNavigate, Navigate } from "react-router-dom";
import { Paper, Title, Text, Button, Container, Group } from "@mantine/core";
import { useForm } from "@mantine/form";
import PasswordInputLocal from "../../Components/PasswordInputLocal";
import { PutData } from "../../Js/FetchData";
import { useStyles } from "./commonStyles";

export default function PasswordReset() {
  const navigate = useNavigate();
  const location = useLocation();
  const { classes } = useStyles();
  const form = useForm({
    initialValues: {
      password: "",
      confirmPassword: "",
    },

    validate: {
      confirmPassword: (value, values) =>
        value !== values.password ? "Passwords did not match" : null,
    },
  });

  const id = location.state?.adminId;

  // useEffect(() => {
  //   if (!id) {
  //     navigate("/login");
  //   }
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, []);
  if (!id) return <Navigate to={"/"} />;

  const url = "https://maheshvattem2.herokuapp.com/admin/resetPassword/" + id;
  const handleSubmit = (values) => {
    // console.log(values);
    const response = PutData(url, {
      password: values.password,
      confirm: values.confirmPassword,
    });
    response
      .then((res) => {
        console.log(res);
        navigate("/login");
      })
      .catch((err) => {
        console.log(err);
      });
  };
  return (
    <>
      <Container size={460} my={30}>
        <Title className={classes.title} ta="center">
          Password Reset
        </Title>

        <Text color="dimmed" size="sm" ta="center">
          Create a new password to continue
        </Text>
        <Paper withBorder shadow="md" p={30} radius="md" mt="xl">
          <PasswordInputLocal
            label="New password"
            placeholder="********"
            variant="default"
            mt="md"
            {...form.getInputProps("password")}
          />
          <PasswordInputLocal
            label="Confirm new password"
            placeholder="********"
            variant="default"
            mt="md"
            {...form.getInputProps("confirmPassword")}
          />
          <Group justify="flex-end" mt="lg" className={classes.controls}>
            <Button
              onClick={form.onSubmit((values) => handleSubmit(values))}
              className={classes.control}
            >
              Reset password
            </Button>
          </Group>
        </Paper>
      </Container>
    </>
  );
}

// <div>
//   <label style={{ display: "block" }} htmlFor="np2">
//     Confirm New Password
//   </label>
//   <input
//     style={{ width: 250 }}
//     type="password"
//     id="np2"
//     onChange={handlepass2}
//     value={pass2}
//     placeholder="Confirm your new password"
//     required
//   />
// </div>

// <input type="submit" />
// </form> */}
// {
//   /* <Anchor color="dimmed" size="sm" className={classes.control}>
// <Center inline>
//   <ArrowLeftIcon size={12} />
//   <Box ml={5}>Back to login page</Box>
// </Center>
// </Anchor> */
// }
