import { Button } from "@mantine/core";
import { useSelector } from "react-redux";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import NoRoomsAdded from "./NoRoomsAdded";

export default function MyRooms() {
  const params = useParams();
  const location = useLocation();
  const rooms = useSelector(
    (state) => state.hostels.hostelsList[params.hid]?.roomsAvailable
  );
  return (
    <>
      <h6>My Rooms</h6>
      {rooms ? "" : <NoRoomsAdded />}
    </>
  );
}
