import { Text, Paper, Group, Button } from "@mantine/core";
import { useNavigate } from "react-router-dom";
export default function NoRoomsAdded() {
  const navigate = useNavigate();

  return (
    <>
      <Paper
        style={{ textAlign: "center" }}
        shadow="sm"
        radius="md"
        p="lg"
        withBorder
      >
        <Text weight="bold" size="lg">
          You haven't registered any Rooms for your hostel yet!
        </Text>
        <Text size="xs" color="dimmed">
          Use the below "Add Room" button to add a Room
        </Text>
        <Group position="center" mt="sm">
          <Button onClick={() => navigate("../addroom")}>Add Room</Button>
        </Group>
      </Paper>
    </>
  );
}
