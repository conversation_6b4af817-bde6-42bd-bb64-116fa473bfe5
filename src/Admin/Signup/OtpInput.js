import React, { useState } from "react";
import fetchData from "../../Js/FetchData";
import {
  Paper,
  Title,
  Text,
  Button,
  Container,
  Group,
  Anchor,
  Center,
  Box,
  Input,
  InputWrapper,
} from "@mantine/core";
import { useStyles } from "../PasswordRecovery/commonStyles";
import { useNavigate } from "react-router-dom";
function OtpInput() {
  const [otp, setOtp] = useState("");
  const navigate = useNavigate();
  const { classes } = useStyles();
  const otpUrl =
    "https://maheshvattem2.herokuapp.com/admin/verifyOtp/" +
    sessionStorage.getItem("mysnoutadminid");
  const handleChange = (e) => {
    setOtp(e.currentTarget.value);
  };
  const handleSubmit = (e) => {
    e.preventDefault();
    fetchData(otpUrl, { otp: otp }).then((res) => {
      console.log(res);
      navigate("/login");
    });
  };
  return (
    <>
      <Container size={460} my={30}>
        <Title className={classes.title} ta="center">
          Otp Verification
        </Title>
        <Text color="dimmed" size="sm" ta="center">
          Enter Otp send to your registered email to complete your signup
          process
        </Text>

        <Paper withBorder shadow="md" p={30} radius="md" mt="xl">
          <InputWrapper label="Otp" required>
            <Input placeholder="Enter code" onChange={handleChange} />
          </InputWrapper>

          <Group justify="space-between" mt="lg" className={classes.controls}>
            <Anchor color="dimmed" size="sm" className={classes.control}>
              <Center inline>
                {/* <ArrowLeftIcon size={12} /> */}
                <Box ml={5}>Resent Otp</Box>
              </Center>
            </Anchor>
            <Button className={classes.control} onClick={handleSubmit}>
              Verify Otp
            </Button>
          </Group>
        </Paper>
      </Container>
    </>
  );
}
export default OtpInput;

/*
      <form onSubmit={handleSubmit}>
        <input
          type="text"
          value={otp}
          ref={Otp}
          onChange={handleChange}
        ></input>
        <input type="submit"></input>
      </form>
*/
