import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { SignupReq } from "../../Store/auth";
import { useDispatch } from "react-redux";
import { useForm } from "@mantine/form";
import PasswordInputLocal from "../../Components/PasswordInputLocal";
import { Loader } from "@mantine/core";
import {
  Input,
  Checkbox,
  Anchor,
  Paper,
  Title,
  // Text,
  Container,
  Group,
  Button,
  InputWrapper,
  createStyles,
} from "@mantine/core";

const useStyles = createStyles((theme) => ({
  "*": {
    boxSizing: "border-box",
  },

  paper: {
    padding: "30px",
    marginTop: "30px",
    // boxShadow: `${theme.shadows.md}`,
    maxWidth: "432px",
  },
  Container: {
    marginLeft: "auto",
    marginRight: "auto",
  },
}));

function Signup() {
  const { classes } = useStyles();
  const dispatch = useDispatch();
  const [isLoggingin, setLoggingIn] = useState(false);
  const navigate = useNavigate();
  const form = useForm({
    initialValues: {
      email: "",
      mobilenum: "",
      name: "",
      password: "",
      terms: true,
    },
    // validationRules: {
    //   email: (val) => /^\S+@\S+$/.test(val),
    //   password: (val) => val.length >= 6,
    // },
  });

  const handleSubmit = (values) => {
    console.log(values);
    setLoggingIn(true);
    dispatch(
      SignupReq(values.name, values.email, values.mobilenum, values.password)
    )
      .then((response) => {
        console.log(response);
        setLoggingIn(false);
        sessionStorage.setItem("mysnoutadminid", response.adminId);
        navigate("/signup/otp", { state: response });
      })
      .catch((error) => {
        console.log(error);
        setLoggingIn(false);
      });
  };

  return (
    <>
      <Container className={classes.Container} size={464} my={40}>
        <Title
          align="center"
          sx={(theme) => ({
            fontFamily: `Greycliff CF, ${theme.fontFamily}`,
            fontWeight: 900,
            fontSize: "28px",
          })}
        >
          Mysnout Signup
        </Title>
        <Paper className={classes.paper} withBorder radius="md">
          <InputWrapper label="Full Name">
            <Input
              placeholder="John Doe"
              variant="default"
              {...form.getInputProps("name")}
              disabled={isLoggingin}
            />
          </InputWrapper>
          <InputWrapper mt="md" label="Mobile Number">
            <Input
              placeholder="1234567890"
              variant="default"
              {...form.getInputProps("mobilenum")}
              disabled={isLoggingin}
            />
          </InputWrapper>

          <InputWrapper mt="md" label="Email">
            <Input
              placeholder="<EMAIL>"
              variant="default"
              {...form.getInputProps("email")}
              disabled={isLoggingin}
            />
          </InputWrapper>

          <PasswordInputLocal
            label="Password"
            placeholder="Your password"
            variant="default"
            mt="md"
            {...form.getInputProps("password")}
            disabled={isLoggingin}
            autocomplete="new-password"
          />
          {/* <PasswordInput
            label="Password"
            placeholder="Your password"
            required
            mt="md"
            variant="default"
            {...form.getInputProps("pass")}
          /> */}
          <Group mt="md">
            <Checkbox
              color="violet"
              label="I accept terms and conditions"
              checked={form.values.terms}
              onChange={(event) =>
                form.setFieldValue("terms", event.currentTarget.checked)
              }
            />
          </Group>
          <Group position="apart" mt="xl">
            <Anchor
              onClick={() => navigate("/login")}
              component="button"
              type="button"
              color="gray"
              size="xs"
            >
              Already have an account? Login
            </Anchor>
            <Button
              color="violet"
              onClick={form.onSubmit(handleSubmit)}
              disabled={isLoggingin}
              rightIcon={
                isLoggingin ? (
                  <Loader size="24" color="violet" variant="dots" />
                ) : (
                  ""
                )
              }
            >
              {isLoggingin ? "Signing Up" : "Register"}
              {/* <Loader size="32" color="teal" variant="dots" /> */}
            </Button>
          </Group>
          <Group position="right"></Group>
        </Paper>
      </Container>
    </>
  );
}

export default Signup;

// old code
/** 
  const [email, setEmail] = useState("");
  const [pass, setPassword] = useState("");
  const [fname, setFname] = useState("");
  const [mobilenum, setMobilenum] = useState("");

const handleSubmit = async (e) => {
  e.preventDefault();
  const response = await fetchData(
    "https://maheshvattem2.herokuapp.com/admin/register",
    {
      fullName: fname,
      email: email,
      mobileNumber: mobilenum,
      password: pass,
    }
  );
  console.log(response);
  sessionStorage.setItem("mysnoutadminid", response.adminId);
  navigate("/signup/otp");
};
console.log(response);
sessionStorage.setItem("mysnoutadminid", response.adminId);
navigate("/signup/otp");

handling input
const handleEvent = (e) => {
  if (e.currentTarget.name === "emailAddress") {
    setEmail(e.currentTarget.value);
  } else if (e.currentTarget.name === "password") {
    setPassword(e.currentTarget.value);
  } else if (e.currentTarget.name === "mobilenumber") {
    setMobilenum(e.currentTarget.value);
  } else {
    setFname(e.currentTarget.value);
  }
};
const emailAddress = useRef();
const password = useRef();
const fullName = useRef();
const mobileNumber = useRef();


*/

// old html code
/**
    <div style={{ maxWidth: 770 }} className="container">
      <h3 style={{ textAlign: "center", padding: 20 }}>Signup</h3>
      <form className="row g-3" onSubmit={handleSubmit}>
        <div className="col-md-6">
          <label htmlFor="fullname" className="form-label">
            Full Name
          </label>
          <input
            type="text"
            className="form-control"
            value={fname}
            // ref={fullName}
            id="fullname"
            name="fullname"
            onChange={handleChange(setFname)}
            required
          />
        </div>
        <div className="col-md-6">
          <label htmlFor="mobilenumber" className="form-label">
            Mobile Number
          </label>
          <input
            type="text"
            className="form-control"
            value={mobilenum}
            // ref={mobileNumber}
            id="mobilenumber"
            name="mobilenumber"
            onChange={handleChange(setMobilenum)}
            required
          />
        </div>
        <div className="col-12">
          <label htmlFor="emailAddress" className="form-label">
            Email
          </label>
          <input
            type="email"
            className="form-control"
            id="emailAddress"
            name="emailAddress"
            placeholder="Enter your email address"
            value={email}
            // ref={emailAddress}
            onChange={handleChange(setEmail)}
            required
          />
        </div>
        <div className="col-12">
          <label htmlFor="password" className="form-label">
            Password
          </label>
          <input
            type="password"
            className="form-control"
            id="password"
            name="password"
            placeholder="Enter your password"
            value={pass}
            // ref={password}
            onChange={handleChange(setPassword)}
            required
          />
        </div>

        <div className="col-12">
          <button type="submit" className="btn btn-primary">
            Sign in
          </button>
        </div>
      </form>
    </div>

 */
