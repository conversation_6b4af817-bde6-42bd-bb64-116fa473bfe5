import { MantineProvider, createTheme } from "@mantine/core";
import { theme as customTheme } from "./Components/themeObject";
import LandingPage from "./LandingPage";
import '@mantine/core/styles.css';

const theme = createTheme({
  ...customTheme,
  defaultColorScheme: 'light',
});

export default function App() {
  return (
    <MantineProvider theme={theme}>
      <LandingPage />
    </MantineProvider>
  );
}
