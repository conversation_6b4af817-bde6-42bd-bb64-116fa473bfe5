import { MantineProvider, ColorSchemeProvider } from "@mantine/core";
import { theme } from "./Components/themeObject";
import { useState } from "react";
import LandingPage from "./LandingPage";

export default function App() {
  const [colorScheme, setColorScheme] = useState(
    localStorage.getItem("colorScheme") || "light"
  );
  const toggleColorScheme = (value) =>
    setColorScheme(value || (colorScheme === "dark" ? "light" : "dark"));
  localStorage.setItem("colorScheme", colorScheme);

  return (
    <ColorSchemeProvider
      colorScheme={colorScheme}
      toggleColorScheme={toggleColorScheme}
    >
      <MantineProvider theme={{ ...theme, colorScheme }} withGlobalStyles>
        <LandingPage />
      </MantineProvider>
    </ColorSchemeProvider>
  );
}
