import { AspectRatio, Container, Image } from "@mantine/core";
import { useListState } from "@mantine/hooks";
import { useState } from "react";

export default function DragAndDrop() {
  const [images, handler] = useListState([
    "https://images.unsplash.com/photo-1653987255814-3b4c05832660?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2070&q=80",
    "https://images.unsplash.com/photo-1654004762137-0e4025b88119?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80",
    "https://images.unsplash.com/photo-1654006606383-77272fc14bcd?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=435&q=80",
    "https://images.unsplash.com/photo-1653917139373-86b743f8c6f4?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80",
    "https://images.unsplash.com/photo-1654011904758-c7419f51575b?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80",
  ]);
  return (
    <>
      {/* <AspectRatio ratio={720 / 1080} sx={{ maxWidth: 300 }} mx="auto">
        <Image draggable="true" radius="md" src={images[0]}></Image>
      </AspectRatio> */}

      <Container>
        <div style={{}}>
          <div draggable style={{ display: "inline-block", padding: "5px" }}>
            <img
              src={images[0]}
              style={{ height: "150px", width: "150px" }}
              alt=""
            ></img>
          </div>

          <div draggable style={{ display: "inline-block", padding: "5px" }}>
            <img
              // draggable="true"
              src={images[1]}
              style={{ height: "150px", width: "150px" }}
              alt=""
            ></img>
          </div>
          <div draggable style={{ display: "inline-block", padding: "5px" }}>
            <img
              // draggable="true"
              src={images[2]}
              style={{ height: "150px", width: "150px" }}
              alt=""
            ></img>
          </div>
          <div draggable style={{ display: "inline-block", padding: "5px" }}>
            <img
              // draggable="true"
              src={images[3]}
              style={{ height: "150px", width: "150px" }}
              alt=""
            ></img>
          </div>
          <div draggable style={{ display: "inline-block", padding: "5px" }}>
            <img
              // draggable="true"
              src={images[4]}
              style={{ height: "150px", width: "150px" }}
              alt=""
            ></img>
          </div>
        </div>
        <div draggable>Hello</div>
        <div></div>
      </Container>
    </>
  );
}
