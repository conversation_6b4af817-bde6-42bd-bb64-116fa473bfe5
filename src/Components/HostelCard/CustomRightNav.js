import React from "react";
import { ChevronRight } from "tabler-icons-react";
export const RightNav = React.memo(({ disabled, onClick }) => {
  return (
    <button
      type="button"
      className="image-gallery-icon image-gallery-right-nav"
      disabled={disabled}
      onClick={onClick}
      aria-label="Previous Slide"
    >
      <ChevronRight size={48}></ChevronRight>
    </button>
  );
});
