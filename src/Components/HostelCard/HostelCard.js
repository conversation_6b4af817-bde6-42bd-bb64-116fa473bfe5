import {
  Card,
  Text,
  Badge,
  Button,
  Group,
  useMantineTheme,
} from "@mantine/core";
import {
  IconArrowRight,
  IconLocation,
  IconMail,
  IconPhone,
  IconThumbUp,
  IconUser,
} from "@tabler/icons-react";
import ImageCarousel from "../ImageCarousel";
import { Link } from "react-router-dom";
export default function HostelCard({ hostel }) {
  const theme = useMantineTheme();
  // console.log(hostel);
  const images = hostel.hostelImages.map((image) => ({
    original: image,
    // originalWidth: `340px !important`,
  }));
  const secondaryColor =
    theme.colorScheme === "dark" ? theme.colors.dark[1] : theme.colors.gray[7];

  return (
    <div style={{ maxWidth: 330, margin: "auto" }}>
      <Card shadow="md" p="lg">
        <Card.Section>
          <ImageCarousel images={images} />
        </Card.Section>

        <Group
          justify="space-between"
          style={{ marginBottom: 5, marginTop: theme.spacing.sm }}
        >
          <Text fw={500} size="lg">
            <Link
              style={{ textDecoration: "none" }}
              to={`/myhostels/${hostel._id}`}
            >
              {hostel.hostelName}
            </Link>
          </Text>
          <Badge color="green" variant="light">
            Available
          </Badge>
        </Group>
        <Group mb="xs">
          <IconUser stroke={1.5} />
          <Text>{hostel.wardenName}</Text>
        </Group>
        <Group mb="xs">
          <IconPhone stroke={1.5} />
          <Text>{hostel.hostelContactNumber[0]}</Text>
        </Group>
        <Group mb="xs">
          <IconMail stroke={1.5} />
          <Text>{hostel.hostelEmail}</Text>
        </Group>
        <Group mb="xs" align="flex-start">
          <IconLocation
            style={{ marginTop: "6px" }}
            stroke={1.5}
          />
          <Text style={{ display: "inline-block", width: "250px" }}>
            {hostel.hostelAddress}
          </Text>
        </Group>
        <Group justify="space-between">
          <Group justify="flex-start">
            <IconThumbUp />
            {hostel.likes + " Likes"}
          </Group>
          <Button
            variant="light"
            color="blue"
            rightIcon={<IconArrowRight />}
          >
            <Link
              style={{ textDecoration: "none" }}
              to={`/myhostels/${hostel._id}`}
            >
              More Details
            </Link>
          </Button>
        </Group>

        {/* <Text size="sm" style={{ color: secondaryColor, lineHeight: 1.5 }}>
          With Fjord Tours you can explore more of the magical fjord landscapes
          with tours and activities on and around the fjords of Norway
        </Text> */}
      </Card>
    </div>
  );
}
