.image-gallery-slide .image-gallery-image {
  width: 100%;
  object-fit: cover;
  /* not show=ing border-radius of image oject fit is contain, border-radius is applying to img container not image  */
  height: 180px;
  border-radius: 4px;
}
.image-gallery-slide .image-gallery-image > img {
  border-radius: 4px;
}

.image-gallery-slides {
  line-height: 0;
  overflow: hidden;
  position: relative;
  white-space: nowrap;
  border-radius: 4px;
  text-align: center;
}
