import {
  Card,
  Image,
  Text,
  Badge,
  Button,
  Group,
  useMantineTheme,
} from "@mantine/core";
import {
  IconArrowRight,
  IconLocation,
  IconMail,
  IconPhone,
  IconThumbUp,
  IconUser,
} from "@tabler/icons-react";
import ImageCarousel from "../ImageCarousel";

export default function HostelCard() {
  const theme = useMantineTheme();

  const secondaryColor =
    theme.colorScheme === "dark" ? theme.colors.dark[1] : theme.colors.gray[7];

  return (
    <div style={{ maxWidth: 340, margin: "auto" }}>
      <Card shadow="sm" p="lg">
        <Card.Section p="sm">
          {/* <Image
              src="https://images.unsplash.com/photo-1653917139373-86b743f8c6f4?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80"
              height={160}
              alt="Norway"
            /> */}
          <ImageCarousel />
        </Card.Section>

        <Group
          position="apart"
          style={{ marginBottom: 5, marginTop: theme.spacing.sm }}
        >
          <Text weight={500} size="lg">
            Sri venkateshwara Hostels
          </Text>
          <Group position="right">
            <Badge color="pink" variant="light">
              On Sale
            </Badge>
          </Group>
        </Group>
        <Group mb="xs">
          <IconUser stroke={1.5} />
          <Text>Vattem Mahesh Babu</Text>
        </Group>
        <Group mb="xs">
          <IconPhone stroke={1.5} />
          <Text>6303270256</Text>
        </Group>
        <Group mb="xs">
          <IconMail stroke={1.5} />
          <Text><EMAIL></Text>
        </Group>
        <Group mb="xs" style={{ maxWidth: "300px" }} align="flex-start">
          <IconLocation
            style={{ marginTop: "6px" }}
            stroke={1.5}
          />
          <Text style={{ maxWidth: "250px" }}>
            Door no : 16-18-34, Burle vari street, Old guntur, Guntur, Andhra
            Pradesh, 522001.
          </Text>
        </Group>
        <Group position="apart">
          <Group position="left">
            <IconThumbUp />
            25 Likes
          </Group>
          <Button
            variant="light"
            color="blue"
            rightIcon={<IconArrowRight />}
          >
            More Details
          </Button>
        </Group>

        {/* <Text size="sm" style={{ color: secondaryColor, lineHeight: 1.5 }}>
            With Fjord Tours you can explore more of the magical fjord landscapes
            with tours and activities on and around the fjords of Norway
          </Text> */}
      </Card>
    </div>
  );
}
