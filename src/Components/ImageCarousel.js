// import SimpleImageSlider from "react-simple-image-slider";
// impo rt "./CSS/HostelCardCSS.css";
import { LeftNav } from "./HostelCard/CustomLeftNav";
import "react-image-gallery/styles/css/image-gallery.css";
import ImageGallery from "react-image-gallery";
import { RightNav } from "./HostelCard/CustomRightNav";
import "./HostelCard/HostelCardCss.css";
export default function ImageCarousel({ images }) {
  return (
    <>
      {images ? (
        <div
          style={
            {
              // width: "340px",
              // minWidth: "270px",
              // // margin: "auto",
            }
          }
        >
          <ImageGallery
            // autoPlay="true"
            showPlayButton={false}
            showFullscreenButton={false}
            showBullets="true"
            lazyLoad="true"
            items={images}
            renderLeftNav={(onClick, disabled) => (
              <LeftNav onClick={onClick} disabled={disabled} />
            )}
            renderRightNav={(onClick, disabled) => (
              <RightNav onClick={onClick} disabled={disabled} />
            )}
          />
        </div>
      ) : (
        ""
      )}
    </>
  );
}
