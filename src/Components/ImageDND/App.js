import { Container } from "@mantine/core";
import { DndProvider } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import DragAndDrop from "./DragAndDrop";
import { DndGridProvider } from "./DndContext";
import images from "./sampleimages";
import { TouchBackend } from "react-dnd-touch-backend";

export function Dnd() {
  const isTouchDevice = () => {
    if ("ontouchstart" in window) {
      return true;
    }
    return false;
  };

  // Assigning backend based on touch support on the device
  const backendForDND = isTouchDevice() ? TouchBackend : HTML5Backend;

  return (
    <>
      <DndProvider backend={backendForDND}>
        <Container>
          <DndGridProvider images={images}>
            <DragAndDrop />
          </DndGridProvider>
        </Container>
      </DndProvider>
    </>
  );
}
