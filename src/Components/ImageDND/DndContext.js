import { useListState } from "@mantine/hooks";
import React, { createContext } from "react";

const GridContext = createContext();

export function DndGridProvider(props) {
  const [images, handler] = useListState(props.images);
  return (
    <>
      <GridContext.Provider value={{ images, moveItem: handler }}>
        {props.children}
      </GridContext.Provider>
    </>
  );
}

export default GridContext;
