import { useDrag, useDrop } from "react-dnd";
import { AspectRatio } from "@mantine/core";
import { useRef, memo } from "react";
const DndItem = memo(({ id, src, moveItem, index }) => {
  const [dragProps, drag] = useDrag(
    () => ({
      type: "IMG",
      item: { id, index },
      collect: (monitor) => ({
        isDragging: monitor.isDragging(),
      }),
    }),
    [index, id]
  );
  const [, drop] = useDrop(
    () => ({
      accept: "IMG",
      hover(hoveredOverItem, monitor) {
        const dragIndex = hoveredOverItem.index;
        const hoverIndex = index;
        if (!ref.current) {
          return;
        }

        if (hoveredOverItem.id === id) {
          return;
        }
        if (hoveredOverItem.index === index) {
          return;
        }

        // // Determine rectangle on screen
        // const hoverBoundingRect = ref.current?.getBoundingClientRect();
        // // Get vertical middle
        // const hoverMiddleY =
        //   (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
        // // Determine mouse position
        // const clientOffset = monitor.getClientOffset();
        // // Get pixels to the top
        // const hoverClientY = clientOffset.y - hoverBoundingRect.top;
        // // Only perform the move when the mouse has crossed half of the items height
        // // When dragging downwards, only move when the cursor is below 50%
        // // When dragging upwards, only move when the cursor is above 50%
        // // Dragging downwards
        // if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
        //   return;
        // }
        // // Dragging upwards
        // if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
        //   return;
        // }
        // console.log(hoveredOverItem.id, id);
        console.log(hoveredOverItem.index, " hovered over ", index);

        moveItem.reorder({ from: dragIndex, to: hoverIndex });

        hoveredOverItem.index = hoverIndex;
      },
    }),
    [index, id]
  );
  const ref = useRef();
  drag(ref);
  drop(ref);
  console.log(1);
  return (
    <>
      <div
        ref={ref}
        style={{
          opacity: dragProps.isDragging ? 0.5 : 1,
        }}
      >
        <AspectRatio ratio={3 / 2}>
          <div
            style={{
              backgroundImage: `url(${src})`,
              backgroundPosition: "50%",
              backgroundSize: "cover",
              cursor: "-webkit-grab",
              border: "4px solid white",
              borderRadius: "2px",
            }}
          >
            id - {id}
          </div>
        </AspectRatio>
      </div>
    </>
  );
});
export default DndItem;
