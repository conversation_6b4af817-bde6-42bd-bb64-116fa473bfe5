import { SimpleGrid, Image, AspectRatio } from "@mantine/core";
import { useContext, useEffect } from "react";
import DndItem from "./DndItem";
import GridContext from "./DndContext";
export default function DragAndDrop() {
  const { images, moveItem } = useContext(GridContext);

  return (
    <>
      <SimpleGrid
        cols={4}
        style={{ width: "100%", margin: "auto" }}
        breakpoints={[
          { maxWidth: 750, cols: 3, spacing: "sm" },
          { maxWidth: 600, cols: 3, spacing: "sm" },
          { maxWidth: 380, cols: 2, spacing: "sm" },
        ]}
      >
        {images.map((img, index) => (
          <DndItem
            moveItem={moveItem}
            key={img.id + "key"}
            id={img.id}
            src={img.src}
            index={index}
          />
        ))}
      </SimpleGrid>
    </>
  );
}
