import { SimpleGrid, Image, AspectRatio } from "@mantine/core";
import { useContext, useEffect } from "react";
import DndItem from "./DndItem";
import GridContext from "./DndContext";
export default function DragAndDrop() {
  const { images, moveItem } = useContext(GridContext);

  return (
    <>
      <SimpleGrid
        cols={{ base: 2, sm: 3, md: 4 }}
        spacing="sm"
        style={{ width: "100%", margin: "auto" }}
      >
        {images.map((img, index) => (
          <DndItem
            moveItem={moveItem}
            key={img.id + "key"}
            id={img.id}
            src={img.src}
            index={index}
          />
        ))}
      </SimpleGrid>
    </>
  );
}
