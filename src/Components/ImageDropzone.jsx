import React, { useRef } from "react";
import {
  Text,
  Group,
  Button,
  useMantineTheme,
} from "@mantine/core";
import { Dropzone, MIME_TYPES } from "@mantine/dropzone";
import { IconCloudUpload } from "@tabler/icons-react";
const styles = {
  wrapper: {
    position: "relative",
    marginBottom: 30,
  },
  dropzone: {
    borderWidth: 1,
    paddingBottom: 50,
  },
  control: {
    position: "absolute",
    width: 250,
    left: "calc(50% - 125px)",
    bottom: -20,
  },
};

function getActiveColor(status, theme) {
  return status.accepted
    ? theme.colors[theme.primaryColor][6]
    : status.rejected
    ? theme.colors.red[6]
    : theme.colorScheme === "dark"
    ? theme.colors.dark[0]
    : theme.black;
}

export default function ImageDropZone({ setImages }) {
  const theme = useMantineTheme();
  const openRef = useRef();
  console.log(openRef);

  return (
    <div style={styles.wrapper}>
      <Dropzone
        openRef={openRef}
        onDrop={(imgs) => {
          setImages(imgs);
        }}
        onReject={(imgs) => {
          console.log(imgs);
        }}
        style={styles.dropzone}
        radius="md"
        accept={[MIME_TYPES.png, MIME_TYPES.jpeg]}
        maxSize={5 * 1024 ** 2}
      >
        {(status) => (
          <div style={{ pointerEvents: "none" }}>
            <Group justify="center">
              <IconCloudUpload size={50} color={getActiveColor(status, theme)} />
            </Group>
            <Text
              ta="center"
              fw={700}
              size="lg"
              mt="xl"
              style={{ color: getActiveColor(status, theme) }}
            >
              {status.accepted
                ? "Drop files here"
                : status.rejected
                ? "Image size less than 5mb"
                : "Upload Images"}
              {console.log(status)}
            </Text>
            <Text ta="center" size="sm" mt="xs" color="dimmed">
              Drag&apos;n&apos;drop files here to upload. We can accept only{" "}
              <i>JPEG, PNG</i> files that are less than 5mb in size.
            </Text>
          </div>
        )}
      </Dropzone>

      <Button
        style={styles.control}
        size="md"
        radius="xl"
        onClick={() => openRef.current()}
      >
        Select files
      </Button>
    </div>
  );
}
