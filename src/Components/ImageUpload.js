import React, { useRef, useState } from "react";
import { <PERSON>dal, Button, Group } from "@mantine/core";
import ImageViewer from "./ImagesViewer";
import "../Css/HostleDetails.css";

const ImageUpload = React.memo((props) => Imageupload(props));
function Imageupload(props) {
  const [opened, setOpened] = useState(false);
  const images = useRef([]);
  const input = useRef();
  const preview = useRef();
  const arr = [];
  const updateImageDisplay = () => {
    while (preview.current.firstChild) {
      preview.current.removeChild(preview.current.firstChild);
    }
    const curFiles = input.current.files;
    images.current = curFiles;

    if (curFiles.length === 0) {
      const para = document.createElement("p");
      para.textContent = "No files currently selected for upload";
      preview.current.appendChild(para);
    } else {
      const list = document.createElement("ol");
      preview.current.appendChild(list);

      for (const file of curFiles) {
        const listItem = document.createElement("li");
        const para = document.createElement("p");
        arr.push(file);
        if (validFileType(file)) {
          para.textContent = `File name ${
            file.name
          }, file size ${returnFileSize(file.size)}.`;
          // const image = document.createElement("img");
          // image.src = URL.createObjectURL(file);

          // listItem.appendChild(image);
          listItem.appendChild(para);
        } else {
          para.textContent = `File name ${file.name}: Not a valid file type. Update your selection.`;
          listItem.appendChild(para);
        }
        list.appendChild(listItem);
        images.current = arr;
        props.setImages(arr);
      }
    }
  };
  const fileTypes = [
    "image/apng",
    "image/bmp",
    "image/gif",
    "image/jpeg",
    "image/pjpeg",
    "image/png",
    "image/svg+xml",
    "image/tiff",
    "image/webp",
    `image/x-icon`,
  ];

  function validFileType(file) {
    return fileTypes.includes(file.type);
  }

  function returnFileSize(number) {
    if (number < 1024) {
      return number + "bytes";
    } else if (number > 1024 && number < 1048576) {
      return (number / 1024).toFixed(1) + "KB";
    } else if (number > 1048576) {
      return (number / 1048576).toFixed(1) + "MB";
    }
  }
  if (input.current?.files?.length === 0) {
    images.current = [];
  }
  return (
    <div style={{ marginTop: "10px" }}>
      <div>
        <label id="lable" htmlFor="image_uploads">
          Choose images to upload (PNG, JPG)
        </label>
        <input
          type="file"
          ref={input}
          id="image_uploads"
          style={{ opacity: 0 }}
          onChange={updateImageDisplay}
          multiple
          accept=".png, .jpg, .jpeg"
        />
        <p ref={preview}>No files currently selected for upload</p>
        <Modal
          opened={opened}
          onClose={() => setOpened(false)}
          title="Hostle Images!"
          size="70%"
          overflow="inside"
          transition="fade"
          transitionDuration={600}
          // transitionTimingFunction="ease"
        >
          <ImageViewer images={images.current} />
        </Modal>

        <Group
          position=""
          sx={{
            margin: "2px",
          }}
        >
          <Button variant="outline" onClick={() => setOpened(true)}>
            View Images
          </Button>
        </Group>
        {console.log("rendered ImageUpload")}
      </div>
    </div>
  );
}
export default ImageUpload;
