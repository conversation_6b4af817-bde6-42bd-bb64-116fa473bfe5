import { Image } from "@mantine/core";
function ImageViewer(props) {
  const images = props.images;

  return (
    <>
      <div>
        {images.map((img) => {
          return (
            <div
              key={img.name}
              style={{
                width: "300px",
                height: "200px",
                float: "left",
                margin: "5px",
              }}
            >
              <Image
                radius="md"
                src={URL.createObjectURL(img)}
                alt=""
                fit="contain"
                width={300}
                height={200}
              />
            </div>
          );
        })}
      </div>
    </>
  );
}
export default ImageViewer;

// import { Image } from "@mantine/core";
// function ImageViewer(props) {
//   const images = props.images;
//   return (
//     <>
//       <div>
//         {images.map((img) => {
//           return (
//             <div
//               key={img.name}
//               style={{
//                 width: "300px",
//                 height: "200px",
//                 float: "left",
//                 margin: "5px",
//               }}
//             >
//               <Image
//                 radius="md"
//                 src={URL.createObjectURL(img)}
//                 alt=""
//                 fit="contain"
//                 width={300}
//                 height={200}
//               />
//             </div>
//           );
//         })}
//       </div>
//     </>
//   );
// }
// export default ImageViewer;
