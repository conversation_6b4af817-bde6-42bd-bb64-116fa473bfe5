import React from "react";
import {
  createStyles,
  // Menu,
  // Center,
  Header,
  Container,
  Group,
  // <PERSON><PERSON>,
  Burger,
  use<PERSON>antineTheme,
  Drawer,
} from "@mantine/core";
import { useBooleanToggle } from "@mantine/hooks";
// import { ChevronDown } from "tabler-icons-react";
import DarkModeToggle from "./DarkModeToggle";
import { NavLink } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { LogoutReq } from "../Store/auth";

const HEADER_HEIGHT = 60;

const useStyles = createStyles((theme) => ({
  nav: {
    position: "sticky",
    top: "0",
    // background-color: green;
    // border: 2px solid #4CAF50;
    // backgroundColor: "rgba(255, 255, 255, 0.15)",
    background: `${
      theme.colorScheme === "dark" ? "rgba(0,0,0,0.5)" : "hsla(0,0%,100%,0.8)"
    }`,
    backdropFilter: "saturate(180%) blur(10px)",
    // boxShadow: "rgb(2 1 1 / 10%) 0px 5px 20px -5px",
  },
  inner: {
    height: HEADER_HEIGHT,
    display: "flex",
    flexDirection: "rows",
    justifyContent: "space-between",
    alignItems: "center",
  },
  push: {
    marginLeft: "auto",
  },
  links: {
    [theme.fn.smallerThan("xs")]: {
      display: "none",
    },
  },

  burger: {
    [theme.fn.largerThan("xs")]: {
      display: "none",
    },
  },

  link: {
    display: "block",
    lineHeight: 1,
    padding: "8px 12px",
    borderRadius: theme.radius.sm,
    textDecoration: "none",
    color:
      theme.colorScheme === "dark"
        ? theme.colors.dark[0]
        : theme.colors.gray[7],
    fontSize: theme.fontSizes.sm,
    fontWeight: 500,

    "&:hover": {
      backgroundColor:
        theme.colorScheme === "dark"
          ? theme.colors.dark[6]
          : theme.colors.gray[0],
      color:
        theme.colorScheme === "dark"
          ? theme.colors.dark[0]
          : theme.colors.gray[7],
    },
  },

  linkLabel: {
    marginRight: 5,
  },

  logoutBorder: {
    border: `2px solid ${theme.colors.violet[4]}`,
  },
}));
const style = (theme) => {
  return {
    color:
      theme.colorScheme === "dark"
        ? theme.colors.dark[0]
        : theme.colors.gray[7],
    // borderBottom: `2px solid ${theme.colors.violet[4]}`,
    // backgroundColor:
    //   theme.colorScheme === "dark"
    //     ? "#032B3A"
    //     : theme.colors.gray[0],
    textDecoration: "underline",
    textUnderlineOffset: "4px",
    textDecorationColor: ` ${theme.colors.violet[4]}`,
    textDecorationThickness: "2px",
    fontSize: "16px",
    fontWeight: "bold",
  };
};

// interface HeaderActionProps {
//   links: { link: string; label: string; links: { link: string; label: string }[] }[];
// }
// export function HeaderAction({ links }: HeaderActionProps) {

export default function NavbarIn() {
  const { classes } = useStyles();
  const theme = useMantineTheme();
  const isLoggedIn = useSelector((store) => store.auth.isLoggedIn);
  const dispatch = useDispatch();
  const [opened, toggleOpened] = useBooleanToggle(false);
  const title = opened ? "Close navigation" : "Open navigation";

  const links = [
    { link: "/login", label: "Login" },
    { link: "/signup", label: "Register" },
  ];
  const items = links.map((link) => {
    // const menuItems = link.links?.map((item) => (
    //   <Menu.Item key={item.link}>{item.label}</Menu.Item>
    // ));

    // if (menuItems) {
    //   return (
    //     <Menu
    //       key={link.label}
    //       trigger="hover"
    //       delay={0}
    //       transitionDuration={0}
    //       placement="end"
    //       gutter={1}
    //       control={
    //         <a
    //           href={link.link}
    //           className={classes.link}
    //           onClick={(event) => event.preventDefault()}
    //         >
    //           <Center>
    //             <span className={classes.linkLabel}>{link.label}</span>
    //             <ChevronDown size={12} />
    //           </Center>
    //         </a>
    //       }
    //     >
    //       {menuItems}
    //     </Menu>
    //   );
    // }

    return (
      <NavLink
        key={link.label}
        to={link.link}
        style={(isActive) => (isActive.isActive ? style(theme) : {})}
        className={classes.link}
        onClick={() => {
          if (opened) toggleOpened();
        }}
        // onClick={(event) => event.preventDefault()}
      >
        {link.label}
      </NavLink>
    );
  });

  const linksOnLoggedIn = [
    { link: "/hosteldetails", label: "Add Hostel" },
    { link: "/myhostels", label: "My Hostels" },
  ];
  const itemsOnLoggedIn = linksOnLoggedIn.map((item) => (
    <NavLink
      key={item.link}
      to={item.link}
      style={(isActive) => (isActive.isActive ? style(theme) : {})}
      className={classes.link}
      onClick={() => {
        if (opened) toggleOpened();
      }}

      // onClick={(event) => event.preventDefault()}
    >
      {item.label}
    </NavLink>
  ));
  return (
    <Header
      height={HEADER_HEIGHT}
      className={classes.nav}
      // style={{
      //   position: "sticky",
      //   top: "0",
      //   // background-color: green;
      //   // border: 2px solid #4CAF50;
      //   // backgroundColor: "rgba(255, 255, 255, 0.15)",
      //   background: "hsla(0,0%,100%,0.8)",
      //   backdropFilter: "saturate(180%) blur(10px)",
      //   // boxShadow: "rgb(2 1 1 / 10%) 0px 5px 20px -5px",
      // }}
      sx={{ borderBottom: 0 }}
      mb={0}
    >
      <Container className={classes.inner} fluid>
        <Group>
          <Burger
            opened={opened}
            onClick={() => toggleOpened()}
            className={classes.burger}
            title={title}
            size="sm"
          />

          <Drawer
            opened={opened}
            onClose={() => toggleOpened()}
            title="Mysnout"
            padding="xl"
            size="80%"
          >
            {/* Drawer content */}
            {!isLoggedIn ? items : ""}
            {isLoggedIn ? itemsOnLoggedIn : ""}
          </Drawer>

          <NavLink
            to="/"
            className={classes.link}
            style={(isActive) =>
              isActive.isActive
                ? style(theme)
                : {
                    fontSize: "16px",
                    fontWeight: "bold",
                  }
            }
          >
            Mysnout
          </NavLink>
        </Group>
        {/* <Button radius="xl" sx={{ height: 30 }}>
          Get early access
        </Button> */}

        <Group className={classes.links}>
          {isLoggedIn ? itemsOnLoggedIn : ""}
        </Group>

        <Group position="right" className={classes.push}>
          <Group className={classes.links}>{!isLoggedIn ? items : ""}</Group>

          {isLoggedIn ? (
            <NavLink
              to="/home"
              style={(isActive) => (isActive.isActive ? style(theme) : {})}
              className={classes.link}
              onClick={(event) => {
                event.preventDefault();
                dispatch(LogoutReq());
              }}
            >
              Logout
            </NavLink>
          ) : (
            ""
          )}

          <DarkModeToggle />
        </Group>
      </Container>
    </Header>
  );
}

//
//
//
//
//
// old code
/* 
import { NavLink } from "react-router-dom";
import { Button } from "@mantine/core";

import "../Css/Navbar.css";
import { useDispatch, useSelector } from "react-redux";
import { LogoutReq } from "../Store/auth";
const style = ({ isActive }) => {
  return {
    padding: "2.5px",
    textDecoration: "none",
    margin: "1rem 0",
    color: isActive ? "red" : "#5865F2",
  };
};

function NavbarIn() {
  const isLoggedIn = useSelector((store) => store.auth.isLoggedIn);
  const dispatch = useDispatch();
  return (
    <>
      <nav style={{ width: "100%" }}>
        <NavLink to="/" style={style}>
          home
        </NavLink>
        {!isLoggedIn && (
          <NavLink to="/login" style={style}>
            login
          </NavLink>
        )}
        {!isLoggedIn && (
          <NavLink
            to="/signup"
            // style={{
            //   padding: "2.5px",
            //   textDecoration: "none",
            // }}
            style={style}
          >
            signup
          </NavLink>
        )}

        {isLoggedIn && (
          <NavLink to="/hostledetails" style={style}>
            HostleDetails
          </NavLink>
        )}

        {isLoggedIn && (
          <NavLink to="/myhostels" style={style}>
            myhostels
          </NavLink>
        )}

        {isLoggedIn && (
          <Button
            variant="outline"
            style={{ float: "right", marginRight: "2px" }}
            onClick={(e) => {
              e.preventDefault();
              dispatch(LogoutReq());
            }}
          >
            Logout
          </Button>
        )}
      </nav>
    </>
  );
}
export default NavbarIn;

*/
