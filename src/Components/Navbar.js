import React from "react";
import {
  Contain<PERSON>,
  <PERSON>,
  Burger,
  Drawer,
} from "@mantine/core";
import { useToggle } from "@mantine/hooks";
// import { ChevronDown } from "tabler-icons-react";
import DarkModeToggle from "./DarkModeToggle";
import { NavLink } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { LogoutReq } from "../Store/auth";

const HEADER_HEIGHT = 60;

const styles = {
  inner: {
    height: HEADER_HEIGHT,
    display: "flex",
    flexDirection: "rows",
    justifyContent: "space-between",
    alignItems: "center",
  },
  push: {
    marginLeft: "auto",
  },
  links: {
    '@media (max-width: 576px)': {
      display: "none",
    },
  },
  burger: {
    '@media (min-width: 576px)': {
      display: "none",
    },
  },
  link: {
    display: "block",
    lineHeight: 1,
    padding: "8px 12px",
    borderRadius: "4px",
    textDecoration: "none",
    color: "#495057",
    fontSize: "14px",
    fontWeight: 500,
  },
  linkLabel: {
    marginRight: 5,
  },
};
const activeStyle = {
  color: "#495057",
  textDecoration: "underline",
  textUnderlineOffset: "4px",
  textDecorationColor: "#7950f2",
  textDecorationThickness: "2px",
  fontSize: "16px",
  fontWeight: "bold",
};

// interface HeaderActionProps {
//   links: { link: string; label: string; links: { link: string; label: string }[] }[];
// }
// export function HeaderAction({ links }: HeaderActionProps) {

export default function NavbarIn() {
  const isLoggedIn = useSelector((store) => store.auth.isLoggedIn);
  const dispatch = useDispatch();
  const [opened, toggleOpened] = useToggle([false, true]);
  const title = opened ? "Close navigation" : "Open navigation";

  const links = [
    { link: "/login", label: "Login" },
    { link: "/signup", label: "Register" },
  ];
  const items = links.map((link) => {
    // const menuItems = link.links?.map((item) => (
    //   <Menu.Item key={item.link}>{item.label}</Menu.Item>
    // ));

    // if (menuItems) {
    //   return (
    //     <Menu
    //       key={link.label}
    //       trigger="hover"
    //       delay={0}
    //       transitionDuration={0}
    //       placement="end"
    //       gutter={1}
    //       control={
    //         <a
    //           href={link.link}
    //           className={classes.link}
    //           onClick={(event) => event.preventDefault()}
    //         >
    //           <Center>
    //             <span className={classes.linkLabel}>{link.label}</span>
    //             <ChevronDown size={12} />
    //           </Center>
    //         </a>
    //       }
    //     >
    //       {menuItems}
    //     </Menu>
    //   );
    // }

    return (
      <NavLink
        key={link.label}
        to={link.link}
        style={(isActive) => (isActive.isActive ? activeStyle : styles.link)}
        onClick={() => {
          if (opened) toggleOpened();
        }}
        // onClick={(event) => event.preventDefault()}
      >
        {link.label}
      </NavLink>
    );
  });

  const linksOnLoggedIn = [
    { link: "/hosteldetails", label: "Add Hostel" },
    { link: "/myhostels", label: "My Hostels" },
  ];
  const itemsOnLoggedIn = linksOnLoggedIn.map((item) => (
    <NavLink
      key={item.link}
      to={item.link}
      style={(isActive) => (isActive.isActive ? activeStyle : styles.link)}
      onClick={() => {
        if (opened) toggleOpened();
      }}

      // onClick={(event) => event.preventDefault()}
    >
      {item.label}
    </NavLink>
  ));
  return (
    <div
      style={{
        height: HEADER_HEIGHT,
        position: "sticky",
        top: "0",
        background: "hsla(0,0%,100%,0.8)",
        backdropFilter: "saturate(180%) blur(10px)",
        borderBottom: 0,
        marginBottom: 0,
      }}
    >
      <Container style={styles.inner} fluid>
        <Group>
          <Burger
            opened={opened}
            onClick={() => toggleOpened()}
            style={styles.burger}
            title={title}
            size="sm"
          />

          <Drawer
            opened={opened}
            onClose={() => toggleOpened()}
            title="Mysnout"
            padding="xl"
            size="80%"
          >
            {/* Drawer content */}
            {!isLoggedIn ? items : ""}
            {isLoggedIn ? itemsOnLoggedIn : ""}
          </Drawer>

          <NavLink
            to="/"
            style={(isActive) =>
              isActive.isActive
                ? activeStyle
                : {
                    ...styles.link,
                    fontSize: "16px",
                    fontWeight: "bold",
                  }
            }
          >
            Mysnout
          </NavLink>
        </Group>
        {/* <Button radius="xl" sx={{ height: 30 }}>
          Get early access
        </Button> */}

        <Group style={styles.links}>
          {isLoggedIn ? itemsOnLoggedIn : ""}
        </Group>

        <Group position="right" style={styles.push}>
          <Group style={styles.links}>{!isLoggedIn ? items : ""}</Group>

          {isLoggedIn ? (
            <NavLink
              to="/home"
              style={(isActive) => (isActive.isActive ? activeStyle : styles.link)}
              onClick={(event) => {
                event.preventDefault();
                dispatch(LogoutReq());
              }}
            >
              Logout
            </NavLink>
          ) : (
            ""
          )}

          <DarkModeToggle />
        </Group>
      </Container>
    </div>
  );
}

//
//
//
//
//
// old code
/* 
import { NavLink } from "react-router-dom";
import { Button } from "@mantine/core";

import "../Css/Navbar.css";
import { useDispatch, useSelector } from "react-redux";
import { LogoutReq } from "../Store/auth";
const style = ({ isActive }) => {
  return {
    padding: "2.5px",
    textDecoration: "none",
    margin: "1rem 0",
    color: isActive ? "red" : "#5865F2",
  };
};

function NavbarIn() {
  const isLoggedIn = useSelector((store) => store.auth.isLoggedIn);
  const dispatch = useDispatch();
  return (
    <>
      <nav style={{ width: "100%" }}>
        <NavLink to="/" style={style}>
          home
        </NavLink>
        {!isLoggedIn && (
          <NavLink to="/login" style={style}>
            login
          </NavLink>
        )}
        {!isLoggedIn && (
          <NavLink
            to="/signup"
            // style={{
            //   padding: "2.5px",
            //   textDecoration: "none",
            // }}
            style={style}
          >
            signup
          </NavLink>
        )}

        {isLoggedIn && (
          <NavLink to="/hostledetails" style={style}>
            HostleDetails
          </NavLink>
        )}

        {isLoggedIn && (
          <NavLink to="/myhostels" style={style}>
            myhostels
          </NavLink>
        )}

        {isLoggedIn && (
          <Button
            variant="outline"
            style={{ float: "right", marginRight: "2px" }}
            onClick={(e) => {
              e.preventDefault();
              dispatch(LogoutReq());
            }}
          >
            Logout
          </Button>
        )}
      </nav>
    </>
  );
}
export default NavbarIn;

*/
