import React from "react";
import {
  Image,
  Container,
  Title,
  Text,
  Button,
  SimpleGrid,
} from "@mantine/core";
import image from "./image.svg";
import { useNavigate } from "react-router-dom";

const styles = {
  root: {
    paddingTop: 80,
    paddingBottom: 80,
  },
  title: {
    fontWeight: 900,
    fontSize: 34,
    marginBottom: "16px",
    fontFamily: 'Greycliff CF, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif',
  },
  control: {
    '@media (max-width: 768px)': {
      width: "100%",
    },
  },
  mobileImage: {
    '@media (min-width: 768px)': {
      display: "none",
    },
  },
  desktopImage: {
    '@media (max-width: 768px)': {
      display: "none",
    },
  },
};

export default function NotFoundImage() {
  const navigate = useNavigate();

  return (
    <Container style={styles.root}>
      <SimpleGrid
        spacing={80}
        cols={2}
      >
        <Image src={image} style={styles.mobileImage} />
        <div>
          <Title style={styles.title}>Something is not right...</Title>
          <Text color="dimmed" size="lg">
            Page you are trying to open does not exist. You may have mistyped
            the address, or the page has been moved to another URL. If you think
            this is an error contact support.
          </Text>
          <Button
            variant="outline"
            size="md"
            mt="xl"
            style={styles.control}
            onClick={() => navigate("/")}
          >
            Get back to home page
          </Button>
        </div>
        <Image src={image} style={styles.desktopImage} />
      </SimpleGrid>
    </Container>
  );
}
