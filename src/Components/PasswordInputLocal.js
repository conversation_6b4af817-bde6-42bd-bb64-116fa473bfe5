import { useState } from "react";
import { IconEyeOff, IconEye } from "@tabler/icons-react";
import { Input, ActionIcon, InputWrapper } from "@mantine/core";

export default function PasswordInputLocal(props) {
  const [isMasked, setMask] = useState(true);
  const type = isMasked ? { type: "password" } : {};
  // console.log(props);
  const { error, label, mt, ...restOfProps } = props;
  console.log(error);
  return (
    <>
      <InputWrapper label={label} mt={mt} error={error}>
        <Input
          {...type}
          {...restOfProps}
          rightSection={
            <ActionIcon onClick={() => setMask(!isMasked)} variant="hover">
              {isMasked ? <EyeOpenIcon /> : <EyeNoneIcon />}
            </ActionIcon>
          }
        />
      </InputWrapper>
    </>
  );
}
