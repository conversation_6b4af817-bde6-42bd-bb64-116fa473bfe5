// import { Container } from "@mantine/core";
// import "./IR.css";
// export default function IR() {
//   return (
//     <>
//       <Container>
//         <div
//           id="inst_Div"
//           style={{ margin: "5px 0", heigh: "75px", marginBottom: "4%" }}
//         >
//           <span>
//             <img
//               src="http://cdn.indiaresults.com/logos/BOSEOAP.png"
//               width="80"
//               height="81"
//               alt=""
//             />
//           </span>
//           <p>
//             Board of Secondary Education - Andhra Pradesh <br></br>
//             <br></br>
//             <b>AP Board SSC Result 2021 AP Board SSC Result 2021s</b>
//           </p>
//         </div>
//         <table className="table table-borderless">
//           <thead>
//             <tr>
//               <th
//                 class="table-secondary"
//                 style={{ textAlign: "center" }}
//                 scope="col"
//                 colspan="4"
//               >
//                 Personal details
//               </th>
//             </tr>
//           </thead>
//           <tbody>
//             <tr>
//               <td>Name</td>
//               <td>Vattem Trinath</td>
//               <td>Roll Number</td>
//               <td>2214134493</td>
//             </tr>
//             <tr>
//               <td>Father Name</td>
//               <td>Vattem Suresh Babu</td>
//               <td>Center</td>
//               <td>Sattenpalli, Guntur dist.</td>
//             </tr>
//             <tr>
//               <td>Mother Name</td>
//               <td>Vattem Lavanya</td>
//             </tr>
//           </tbody>
//         </table>
//         <div
//           style={{ margin: "5px 0", heigh: "75px", marginBottom: "4%" }}
//         ></div>
//         <table className="table table-bordered">
//           <thead>
//             <tr class="table-secondary">
//               <th scope="col">#</th>
//               <th scope="col">SUbject</th>
//               <th scope="col">Marks</th>
//               <th scope="col">Grade</th>
//             </tr>
//           </thead>
//           <tbody>
//             <tr>
//               <th scope="row">1</th>
//               <td>First Language</td>
//               <td>101</td>
//               <td>S+</td>
//             </tr>
//             <tr>
//               <th scope="row">2</th>
//               <td>Second Language</td>
//               <td>95</td>
//               <td>S+</td>
//             </tr>
//             <tr>
//               <th scope="row">3</th>
//               <td>Third Language</td>
//               <td>105</td>
//               <td>S+</td>
//             </tr>
//             <tr>
//               <th scope="row">4</th>
//               <td>Mathematics</td>
//               <td>110</td>
//               <td>S+</td>
//             </tr>
//             <tr>
//               <th scope="row">5</th>
//               <td>Science</td>
//               <td>115</td>
//               <td>S+</td>
//             </tr>
//             <tr>
//               <th scope="row">6</th>
//               <td>Social Studies</td>
//               <td>80</td>
//               <td>S+</td>
//             </tr>
//           </tbody>
//         </table>
//         <table className="table table-bordered">
//           <thead>
//             <tr>
//               <th
//                 class="table-secondary"
//                 style={{ textAlign: "center" }}
//                 scope="col"
//                 colspan="2"
//               >
//                 Final Result
//               </th>
//             </tr>
//           </thead>
//           <tbody>
//             <tr>
//               <td>Marks</td>
//               <td>606</td>
//             </tr>
//             <tr>
//               <td>Result</td>
//               <td style={{ color: "red", fontWeight: "bold" }}>
//                 Thinchesavu ra bigilu
//               </td>
//             </tr>
//           </tbody>
//         </table>
//       </Container>
//     </>
//   );
// }

// #inst_Div {
//   width: 927px;
//   float: left;
//   border-bottom: 1px solid #ddd;
//   border-top: 1px solid #ddd;
//   background-color: #f6f6f6;
//   padding: 5px;
//   height: 80px;
//   margin: 10px 0;
// }
// #inst_Div span {
//   width: 90px;
//   text-align: center;
//   float: left;
//   border: none;
//   height: 80px;
//   margin-left: 30px;
// }
// #inst_Div p {
//   font-size: 18px;
//   color: #333;
//   text-decoration: none;
//   width: 80%;
//   line-height: 14px;
//   float: left;
//   text-align: center;
//   font-weight: bold;
//   padding-top: 10px;
// }
