import axios from "axios";
async function fetchData(url, data, config) {
  try {
    console.log(data);
    const response = await axios.post(url, data, config);
    return Promise.resolve(response.data);
  } catch (error) {
    // console.log(error.response.data);
    // console.log(error.response.status);
    // console.log(error.response.headers);
    return Promise.reject(error.response.data);
  }
}

async function PutData(url, data) {
  try {
    console.log(data);
    const response = await axios.put(url, { ...data });
    console.log(response.data);
    return response.data;
  } catch (error) {
    // console.log(error.response.data);
    // console.log(error.response.status);
    // console.log(error.response.headers);
    return error.response.data;
  }
}

async function GetData(url, config) {
  try {
    const response = await axios.get(url, config);
    return response.data;
  } catch (error) {
    // console.log(error.response.data);
    // console.log(error.response.status);
    // console.log(error.response.headers);
    return error.response?.data;
  }
}
export default fetchData;
export { GetData, PutData };
// old code
// .then((res) => res.data)
// .then((res) => {
//   callback(res);
// })
// .catch((e) => {
//   console.log(e);
//   return 0;
// });
