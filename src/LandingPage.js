import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import Login from "./Admin/LOGIN/Login";
import Signup from "./Admin/Signup/Signup";
import Home from "./Admin/Home";
import SignupWarp from "./Admin/Signup/SignupWarp";
import OtpInput from "./Admin/Signup/OtpInput";
import NavbarIn from "./Components/Navbar";
import HostelDetails from "./Admin/HostelDetails/HostelDetailsWraper";
import OtpRequest from "./Admin/PasswordRecovery/OtpRequest";
import ForgotPassword from "./Admin/PasswordRecovery/ForgotPassword";
import OtpVerification from "./Admin/PasswordRecovery/OtpVerification";
import PasswordReset from "./Admin/PasswordRecovery/PasswordReset";
import MyHostels from "./Admin/HostelDetails/MyHostels";
import { Navigate } from "react-router-dom";
import { useSelector } from "react-redux";
import HostelDetailsWraper from "./Admin/HostelDetails/HostelsWraper";
import HostelTabs from "./Admin/Hostels/Hostel/HostelTabs";
import AddRoomForm from "./Admin/Rooms/AddRoomForm";
import NotFoundImage from "./Components/PageNotFound/NotFoundImage";
import HostelWrapper from "./Admin/Hostels/Hostel/HostelWraper";
import Test from "./Test";
import MyRooms from "./Admin/Rooms/MyRooms";
import IR from "./IR";
export default function LandingPage() {
  const isLoggedIn = useSelector((store) => store.auth.isLoggedIn);
  return (
    <>
      <Router>
        <NavbarIn />
        {/* <Group>
          <ColorInput value={value} onChange={setValue} />
        </Group> */}
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/login" element={<Login />} />
          <Route path="/signup" element={<SignupWarp />}>
            <Route path="" element={<Signup />} />
            <Route path="otp" element={<OtpInput />} />
          </Route>
          <Route path="/test" element={<Test></Test>} />

          <Route path="/forgotpassword" element={<ForgotPassword />}>
            <Route path="" element={<OtpRequest />} />
            <Route path="verifyotp" element={<OtpVerification />}></Route>
            <Route path="reset" element={<PasswordReset />}></Route>
          </Route>

          <Route
            path="/hosteldetails"
            element={
              isLoggedIn ? <HostelDetails /> : <Navigate to={"/login"} />
            }
          />
          <Route
            path="/myhostels"
            element={
              isLoggedIn ? <HostelDetailsWraper /> : <Navigate to={"/login"} />
            }
          >
            <Route path="" element={<MyHostels />} />
            <Route path=":hid" element={<HostelWrapper />}>
              <Route path="" element={<HostelTabs initialTab={0} />}></Route>
              <Route
                path="features"
                element={<HostelTabs initialTab={1} />}
              ></Route>
              <Route path="rooms" element={<HostelTabs initialTab={2} />}>
                <Route path="" element={<MyRooms />} />
                <Route path="addroom" element={<AddRoomForm />} />
              </Route>
            </Route>
          </Route>
          <Route
            path="*"
            element={
              // <main style={{ padding: "1rem" }}>
              //   <p>There's nothing here!</p>
              // </main>
              <NotFoundImage />
            }
          />
          {/* <Route path="10th-results" element={<IR />} /> */}
        </Routes>
      </Router>
    </>
  );
}

/*     const linksOnLoggedIn = [
      { link: "/hosteldetails", label: "Add Hostel" },
      { link: "/myhostels", label: "My Hostels" },
    ];
    const itemsOnLoggedIn = linksOnLoggedIn.map((item) => (
      <NavLink
        to={item.link}
        style={(isActive) => (isActive.isActive ? style(theme) : {})}
        className={classes.link}
        // onClick={(event) => event.preventDefault()}
      >
        {item.label}
      </NavLink>
    ));
 */
