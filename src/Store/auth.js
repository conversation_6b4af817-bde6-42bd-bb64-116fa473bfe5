import { createSlice } from "@reduxjs/toolkit";
import fetchData from "../Js/FetchData";
// import { persistor } from "./store";

// this is before using redux-persist manually storing serialized auth object in local storage and retreiving it when store gets initailized

// const user = JSON.parse(localStorage.getItem("mysnoutuser"));
// const initialState = user
//   ? { isLoggedIn: true, user, message: { onSuccess: null, onFailure: null } }
//   : {
//       isLoggedIn: false,
//       user: null,
//       message: { onSuccess: null, onFailure: null },
//     };
// if (user) {
//   initialState = { isLoggedIn: true, user: user };
// } else {
//   initialState = { isLoggedIn: false, user: null };
// }

// using redux-persist we just need to set initial state this gets initialized if there is no data stored in localstorage by redux persist
// or if the initialstate to this reducer is null or undefined
const initialState = {
  isLoggedIn: false,
  user: null,
  message: { onSuccess: null, onFailure: null },
};
const slice = createSlice({
  name: "auth",
  initialState: initialState,
  reducers: {
    LoginSuccess: (auth, action) => {
      auth.isLoggedIn = true;
      auth.user = action.payload.user;
      auth.message.onSuccess = action.payload.message;
      auth.message.onFailure = null;
    },
    LoginFailure: (auth, action) => {
      auth.message.onFailure = action.payload.message;
      auth.message.onSuccess = null;
    },
    Logout: (auth, action) => {
      auth.isLoggedIn = false;
      auth.user = null;
      auth.message.onFailure = auth.message.onSuccess = null;
    },
    // action => action handler
  },
});

export const { LoginSuccess, LoginFailure, Logout } = slice.actions;

export default slice.reducer;
const baseURL = "https://mysnoutt.herokuapp.com";

// ~ when loginReq is called with required params, a function is returned with dispatch as single param and when we dispatch this function-
// ~ it is processed by think middleware as it is a function it gets executed as fun(dispatch)

export const loginReq = (email, pass) => (dispatch) =>
  fetchData(baseURL + "/admin/login", {
    email: email,
    password: pass,
  })
    .then((response) => {
      const user = {
        email: email,
        authToken: response.token,
        adminId: response.adminId,
      };
      //   console.log(response);
      // localStorage.setItem("mysnoutuser", JSON.stringify(user));
      dispatch(
        LoginSuccess({
          user: user,
          isLoggedIn: true,
          message: response.message,
        })
      );
      return Promise.resolve();
    })
    .catch((error) => {
      dispatch(LoginFailure({ message: error.message }));
      console.log(error.message);
      return Promise.reject();
    });

export const LogoutReq = () => (dispatch) => {
  dispatch(Logout({}));
  dispatch({ type: "reset" });
  return true;
};

export const SignupReq = (fname, email, mobilenum, pass) => (dispatch) =>
  fetchData("https://maheshvattem2.herokuapp.com/admin/register", {
    fullName: fname,
    email: email,
    mobileNumber: mobilenum,
    password: pass,
  })
    .then((response) => Promise.resolve(response))
    .catch((error) => Promise.reject(error));
