import { createSlice } from "@reduxjs/toolkit";
import fetchData, { GetData } from "../Js/FetchData";
import { apiCallBegan } from "./api";
const initialState = {
  features: {},
  isLoading: false,
};
// * objectId whhich is unique to hostel as key
// * the hostelFeatures is an object which has feature_type as keys and the corresponding features in a list

const slice = createSlice({
  name: "features",
  initialState: initialState,
  reducers: {
    // action => action handler
    AddFeatures: (hostelFeatures, action) => {
      // ~ hostelFeaturesId = key and hostelFeatures ->{} (object of feature types(key) and its values -> list) = value
      hostelFeatures.features[action.payload.hid] =
        action.payload.hosteldetails;
    },
    UpdateFeatures: (hostelFeatures, action) => {
      // ~ hostelFeaturesId = key and hostelFeatures ->{} (object of feature types(key) and its values -> list) = value
      // hostelFeatures.features[action.payload.hid] =
      //   action.payload.hostelFeatures;
      hostelFeatures.isLoading = false;
    },
    CallFailed: (hostelFeatures) => {
      hostelFeatures.isLoading = false;
    },
    CallBegan: (hostelFeatures) => {
      hostelFeatures.isLoading = true;
    },
  },
});
const { AddFeatures } = slice.actions;

export default slice.reducer;

export const GetFeatures = () => (dispatch, store) => {
  const config = {
    headers: {
      Authorization: "Bearer " + store().auth.user?.authToken,
    },
  };
  let url = process.env.REACT_APP_API_URL + "/admin/allHostelFeatures/";
  // console.log(url, process.env.REACT_APP_API_URL);
  return GetData(url, config)
    .then((response) => {
      return Promise.resolve(response);
    })
    .catch((error) => Promise.reject(error));
};

export const postHostelFeatures = (data, hid) => (dispatch, store) => {
  const config = {
    headers: {
      Authorization: "Bearer " + store().auth.user?.authToken,
    },
  };
  const url = process.env.REACT_APP_API_URL + "/admin/hostelFeatures/" + hid;
  return fetchData(url, data, config)
    .then((response) => {
      console.log(response);
      dispatch(GetHostelFeatures(hid));
      return Promise.resolve();
    })
    .catch((error) => Promise.reject(error));
};

export const GetHostelFeatures = (hid) => (dispatch, store) => {
  const config = {
    headers: {
      Authorization: "Bearer " + store().auth.user?.authToken,
    },
  };
  const url = process.env.REACT_APP_API_URL + "/admin/hostelFeatures/" + hid;
  return GetData(url, config)
    .then((response) => {
      console.log(response);
      dispatch(AddFeatures({ hid, ...response }));
      return Promise.resolve();
    })
    .catch((error) => Promise.reject(error));
};

export const UpdateHostelFeatures = (data, hid) => (dispatch) =>
  dispatch(
    apiCallBegan({
      url: "/admin/hostelFeatures/" + hid,
      method: "put",
      data: data,
      onStart: "features/CallBegan",
      onSuccess: "features/UpdateFeatures",
      onError: "features/CallFailed",
      props: { hid },
    })
  ).then(() => dispatch(GetHostelFeatures(hid)).then(() => Promise.resolve()));

/* {
    "success": "true",
    "message": "Hostel Features Added",
    "hostelFeatures": {
        "charges": [
            "60d377712c390b0c73056a36",
            "60d377cd2c390b0c73056a37",
            "60d377fe2c390b0c73056a38"
        ],
        "amenities": [
            "60d369fd2c390b0c73056a1a",
            "60d36b3f2c390b0c73056a1b",
            "60d36b9c2c390b0c73056a1c",
            "60d36ebf2c390b0c73056a1d",
            "60d36eea2c390b0c73056a1e",
            "60d36f1c2c390b0c73056a1f",
            "60d3704e2c390b0c73056a20",
            "60d370c22c390b0c73056a21",
            "60d371d22c390b0c73056a23"
        ],
        "hostelPrefferedFor": [],
        "hostelAvailableFor": [
            "60d3739a2c390b0c73056a2a"
        ],
        "cleaningServices": [
            "60d3767d2c390b0c73056a32",
            "60d376ca2c390b0c73056a33",
            "60d377022c390b0c73056a34",
            "60d377412c390b0c73056a35"
        ],
        "security": [
            "60d378df2c390b0c73056a3a",
            "60d378b92c390b0c73056a39"
        ],
        "homeRules": [
            "60d375a42c390b0c73056a2f",
            "60d376012c390b0c73056a30",
            "60d376312c390b0c73056a31"
        ],
        "facilities": [
            "60d372422c390b0c73056a24",
            "60d372702c390b0c73056a25",
            "60d372b22c390b0c73056a26",
            "60d372e82c390b0c73056a27",
            "60d3731e2c390b0c73056a28",
            "60d373622c390b0c73056a29"
        ],
        "customFacilities": [
            "From test 321"
        ],
        "_id": "62679f50b05533809d4f1680",
        "__v": 0
    }
} */
