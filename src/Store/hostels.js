import { createSlice } from "@reduxjs/toolkit";
import { hostelDetailsResponseJsonPrototype as objCopy } from "./Constants/hostelDetailsjson";
import fetchData, { GetData } from "../Js/FetchData";
// import AuthHeader from "./Constants/AuthHeader";
const initialState = {
  length: 0,
  isFetched: false,
  hostelsList: {},
};
const hostelSlice = createSlice({
  name: "hostels",
  initialState,
  reducers: {
    AddHostel: (store_hostels, action) => {
      const hostel = Object.assign(objCopy);
      const response = action.payload.hostelDetails;
      Object.keys(hostel).map((key) => (hostel[key] = response[key]));
      store_hostels.hostelsList[hostel._id] = { ...hostel };
      store_hostels.length++;
    },
    hydrateHostel: (store_hostels, action) => {
      const hostels = action.payload.hostelDetails;
      store_hostels.length = hostels.length;
      hostels.map((hostel) => addHostel(store_hostels, hostel));
      store_hostels.isFetched = true;
    },
  },
});

export const { AddHostel, hydrateHostel } = hostelSlice.actions;
export default hostelSlice.reducer;

// utility functions - reuasble
function addHostel(store_hostels, singleHostel) {
  const hostel = Object.assign(objCopy);
  const response = singleHostel;
  Object.keys(hostel).map((key) => (hostel[key] = response[key]));
  store_hostels.hostelsList[hostel._id] = { ...hostel };
}

// network request functions
const baseUrl = "https://mysnoutt.herokuapp.com";

export const AddHostelRequest = (data) => (dispatch, store) => {
  const config = {
    headers: {
      Authorization: "Bearer " + store().auth.user?.authToken,
      "Content-Type": "multipart/form-data",
    },
  };
  return fetchData(baseUrl + "/admin/hosteldetails", data, config)
    .then((response) => {
      dispatch(AddHostel(response));
      return Promise.resolve();
    })
    .catch((error) => Promise.reject(error));
};

export const GetAllHostels = () => (dispatch, store) => {
  const config = {
    headers: {
      Authorization: "Bearer " + store().auth.user?.authToken,
    },
  };
  return GetData(baseUrl + "/admin/hosteldetails", config)
    .then((response) => {
      console.log(response);
      dispatch(hydrateHostel(response));
      return Promise.resolve();
    })
    .catch((error) => Promise.reject(error));
};

export const GetHostel = (hid) => (dispatch, store) => {
  const config = {
    headers: {
      Authorization: "Bearer " + store().auth.user?.authToken,
    },
  };
  let url = baseUrl + "/admin/hosteldetails/" + hid;

  return GetData(url, config)
    .then((response) => {
      console.log(response);
      dispatch(AddHostel(response));
      return Promise.resolve();
    })
    .catch((error) => Promise.reject(error));
};

// to get all hostel features for server, it requires auth token
