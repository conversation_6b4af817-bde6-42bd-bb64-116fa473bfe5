import axios from "axios";
import * as actions from "../api";

const api =
  ({ dispatch, getState }) =>
  (next) =>
  async (action) => {
    if (action.type !== actions.apiCallBegan.type) return next(action);

    const { url, method, data, onStart, onSuccess, onError, props } =
      action.payload;
    // const config = {
    //   headers: { Authorization: "Bearer " + getState().auth.user?.authToken },
    // };
    const headers = {
      Authorization: "Bearer " + getState().auth.user?.authToken,
    };

    if (onStart) dispatch({ type: onStart });

    next(action);
    // this next(action) is to log the current action in redux devtools
    // console.log(getState());
    try {
      const response = await axios.request({
        baseURL: process.env.REACT_APP_API_URL,
        url,
        method,
        data,
        headers,
      });
      console.log(response);
      // General
      // dispatch(actions.apiCallSuccess(response.data));
      // Specific
      if (onSuccess)
        dispatch({ type: onSuccess, payload: { ...response.data, ...props } });
      return "hello";
    } catch (error) {
      // General
      // dispatch(actions.apiCallFailed(error.message));
      // Specific
      if (onError) dispatch({ type: onError, payload: error.message });
    }
  };

export default api;
