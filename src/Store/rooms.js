import { createSlice } from "@reduxjs/toolkit";
// import fetchData, { GetData } from "../Js/FetchData";
// import { apiCallBegan } from "./api";
const initialState = {
  rooms: {},
  isLoading: false,
};
// * objectId whhich is unique to hostel as key
// * the hostelFeatures is an object which has feature_type as keys and the corresponding features in a list

const slice = createSlice({
  name: "rooms",
  initialState: initialState,
  reducers: {
    // action => action handler
    AddRoom: (rooms, action) => {},
    UpdateFeatures: (rooms, action) => {
      rooms.isLoading = false;
    },
    CallFailed: (rooms) => {
      rooms.isLoading = false;
    },
    CallBegan: (rooms) => {
      rooms.isLoading = true;
    },
  },
});
const { AddRoom } = slice.actions;

export default slice.reducer;
