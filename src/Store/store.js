import { configureStore } from "@reduxjs/toolkit";
import {
  persistStore,
  persistReducer,
  FLUSH,
  REHYDRATE,
  PAUSE,
  PERSIST,
  PURGE,
  REGISTER,
} from "redux-persist";
import api from "./middleware/apiMiddleware";
import storage from "redux-persist/lib/storage"; // defaults to localStorage for web
import reducer from "./reducer";
const persistConfig = {
  key: "root",
  storage,
};
const persistedReducer = persistReducer(persistConfig, reducer);

// import api from "./middleware/api";
// export default function createStore() {

const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
      },
    }).concat(api),
  devTools: process.env.NODE_ENV !== "production",
});
let persistor = persistStore(store);
export { store, persistor };
// }

// middleware: (getDefaultMiddleware) => getDefaultMiddleware(),
