// import { Image } from "@mantine/core";
// import DragAndDrop from "./Components/DragAndDrop";
// import { SimpleGrid, Container } from "@mantine/core";
// import HostelCard from "./Components/HostelCard/HostelCard";
import { Dnd } from "./Components/ImageDND/App";
export default function Test() {
  return (
    <>
      <Dnd></Dnd>
    </>
  );
}

// eslint-disable-next-line no-lone-blocks
{
  /* <Container p={0}>
<SimpleGrid
  cols={2}
  breakpoints={[
    { maxWidth: 980, cols: 2, spacing: "md" },
    { maxWidth: 750, cols: 1, spacing: "sm" },
    { maxWidth: 600, cols: 1, spacing: "sm" },
  ]}
>
  <HostelCard></HostelCard>
  <HostelCard></HostelCard>
  <HostelCard></HostelCard>
</SimpleGrid>
</Container> */
}
