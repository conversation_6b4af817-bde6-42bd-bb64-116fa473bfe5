// Alerts :
// Queries
// TODOs : todo - Orange
// Highlights

// * - important information is highlighted - green
// todo - todos - oranges
// ! - decapritated method do not use - red (Alerts)
// ?? - should this ... ? - blue (may be Queries)
// ~ - to explain how the code works

/* 
todo - create as hostelFeatures slice 
~ key = the objectId stored in the hostelFeatures key in the hostels reducer\
~ 1) create an action to post the features and store the id in the response to hostelFeatures key in the hostels reducer
~ 2) show the features in a good looking component, a get request to fetch the hostel features if features not present in persisted redux state

todo - when accessing a perticular hostel using hid -> baseurl/myhostels/:hid add validation to check it is an id(integer) or not if not navigate to /my hostels and if it is check if it is available in redux store if not fetch it. if it returns hostel render it if not render hostel not found 

 */
// Dev Notes
// * npm install -g dotenv-cli installed and the vscode errors in .env file are gone
